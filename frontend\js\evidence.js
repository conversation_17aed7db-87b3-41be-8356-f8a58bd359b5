// Evidence Management
class EvidenceManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 12;
        this.filters = {};
        this.evidences = [];
        this.totalPages = 0;
        this.init();
    }

    init() {
        this.setupUploadForm();
        this.setupFilters();
        this.setupFilePreview();
    }

    setupUploadForm() {
        const uploadForm = document.getElementById('upload-form');
        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => this.handleUpload(e));
        }
    }

    setupFilters() {
        const applyFiltersBtn = document.getElementById('apply-filters');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => this.applyFilters());
        }

        // Search on enter key
        const searchInput = document.getElementById('search-case');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });
        }
    }

    setupFilePreview() {
        const fileInput = document.getElementById('evidence-file');
        const previewDiv = document.getElementById('file-preview');
        const previewContent = document.getElementById('preview-content');

        if (fileInput && previewDiv && previewContent) {
            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    previewDiv.style.display = 'block';
                    previewFile(file, previewContent);
                } else {
                    previewDiv.style.display = 'none';
                }
            });
        }
    }

    async handleUpload(event) {
        event.preventDefault();

        if (!window.authManager || !window.authManager.canUploadEvidence()) {
            showErrorNotification('Access Denied', 'Only police officers can upload evidence. Please contact your administrator if you need access.');
            return;
        }

        const formData = new FormData(event.target);
        const file = formData.get('evidenceFile');

        // Validate form
        if (!file) {
            showErrorNotification('No File Selected', 'Please choose an evidence file to upload before submitting.');
            return;
        }

        if (!formData.get('caseId') || !formData.get('description')) {
            showErrorNotification('Missing Information', 'Please fill in the Case ID and Description fields. Both are required for evidence submission.');
            return;
        }

        try {
            showLoading();
            
            const response = await api.uploadEvidence(formData);
            
            if (response.success) {
                const evidence = response.evidence;
                showSuccessNotification(
                    'Evidence Uploaded Successfully!',
                    `File: ${evidence.fileName} | Case: ${evidence.caseId} | Evidence ID: ${evidence.evidenceId}${evidence.blockchainTxHash ? ' | Blockchain TX: ' + evidence.blockchainTxHash.substring(0, 10) + '...' : ''}`
                );
                event.target.reset();
                document.getElementById('file-preview').style.display = 'none';

                // Refresh dashboard if available
                if (window.dashboardManager) {
                    window.dashboardManager.refresh();
                }
            } else {
                showErrorNotification('Upload Failed', response.message || 'An error occurred while uploading the evidence. Please try again.');
            }
        } catch (error) {
            console.error('Upload error:', error);
            showErrorNotification('Upload Error', 'Network error occurred during upload. Please check your connection and try again.');
        } finally {
            hideLoading();
        }
    }

    async loadEvidences(page = 1) {
        if (!window.authManager || !window.authManager.canViewEvidence()) {
            showToast('You do not have permission to view evidence', 'error');
            return;
        }

        try {
            showLoading();
            
            const params = {
                page: page,
                limit: this.pageSize,
                ...this.filters
            };

            const response = await api.getEvidences(params);
            
            if (response.success) {
                this.evidences = response.evidences;
                this.currentPage = response.pagination.page;
                this.totalPages = response.pagination.pages;
                
                this.renderEvidenceGrid();
                this.renderPagination();
            } else {
                showToast('Failed to load evidence', 'error');
            }
        } catch (error) {
            console.error('Load evidences error:', error);
            showToast('Error loading evidence. Please try again.', 'error');
        } finally {
            hideLoading();
        }
    }

    renderEvidenceGrid() {
        const evidenceGrid = document.getElementById('evidence-grid');
        if (!evidenceGrid) return;

        if (this.evidences.length === 0) {
            evidenceGrid.innerHTML = `
                <div class="no-evidence">
                    <i class="fas fa-folder-open"></i>
                    <h3>No evidence found</h3>
                    <p>No evidence matches your current filters.</p>
                </div>
            `;
            return;
        }

        const evidenceHTML = this.evidences.map(evidence => this.createEvidenceCard(evidence)).join('');
        evidenceGrid.innerHTML = evidenceHTML;
    }

    createEvidenceCard(evidence) {
        const verificationStatus = evidence.isVerified ? 
            '<span class="verification-status verified"><i class="fas fa-check-circle"></i> Verified</span>' :
            '<span class="verification-status unverified"><i class="fas fa-clock"></i> Unverified</span>';

        return `
            <div class="evidence-card" data-evidence-id="${evidence._id}">
                <div class="evidence-card-header">
                    <h3>${escapeHtml(evidence.originalName)}</h3>
                    <div class="evidence-id">ID: ${evidence.evidenceId}</div>
                </div>
                <div class="evidence-card-body">
                    <div class="evidence-meta">
                        <div class="evidence-meta-item">
                            <strong>Case ID</strong>
                            ${escapeHtml(evidence.caseId)}
                        </div>
                        <div class="evidence-meta-item">
                            <strong>File Size</strong>
                            ${formatFileSize(evidence.fileSize)}
                        </div>
                        <div class="evidence-meta-item">
                            <strong>Uploaded</strong>
                            ${formatDate(evidence.createdAt)}
                        </div>
                        <div class="evidence-meta-item">
                            <strong>Status</strong>
                            ${verificationStatus}
                        </div>
                    </div>
                    <div class="evidence-description">
                        ${escapeHtml(truncateText(evidence.description, 100))}
                    </div>
                    <div class="evidence-actions">
                        <button class="btn btn-primary" onclick="evidenceManager.viewEvidence('${evidence._id}')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="btn btn-secondary" onclick="evidenceManager.downloadEvidence('${evidence._id}', '${escapeHtml(evidence.originalName)}')">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button class="btn btn-success" onclick="evidenceManager.verifyEvidence('${evidence._id}')">
                            <i class="fas fa-check"></i> Verify
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    renderPagination() {
        createPagination(this.currentPage, this.totalPages, 'evidenceManager.loadEvidences');
    }

    applyFilters() {
        const searchCase = document.getElementById('search-case').value;
        const filterVerified = document.getElementById('filter-verified').value;

        this.filters = {};
        
        if (searchCase) {
            this.filters.caseId = searchCase;
        }
        
        if (filterVerified !== '') {
            this.filters.verified = filterVerified;
        }

        this.currentPage = 1;
        this.loadEvidences(1);
    }

    async viewEvidence(evidenceId) {
        try {
            showLoading();
            
            const response = await api.getEvidence(evidenceId);
            
            if (response.success) {
                this.showEvidenceModal(response.evidence);
            } else {
                showToast('Failed to load evidence details', 'error');
            }
        } catch (error) {
            console.error('View evidence error:', error);
            showToast('Error loading evidence details', 'error');
        } finally {
            hideLoading();
        }
    }

    showEvidenceModal(evidence) {
        const modalContent = `
            <div class="evidence-details">
                <div class="evidence-header">
                    <h3>${escapeHtml(evidence.originalName)}</h3>
                    <span class="evidence-id">Evidence ID: ${evidence.evidenceId}</span>
                </div>
                
                <div class="evidence-info-grid">
                    <div class="info-item">
                        <strong>Case ID:</strong>
                        <span>${escapeHtml(evidence.caseId)}</span>
                    </div>
                    <div class="info-item">
                        <strong>File Hash:</strong>
                        <span class="hash-value" title="${evidence.fileHash}">${evidence.fileHash.substr(0, 16)}...</span>
                        <button class="btn-copy" onclick="copyToClipboard('${evidence.fileHash}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="info-item">
                        <strong>File Size:</strong>
                        <span>${formatFileSize(evidence.fileSize)}</span>
                    </div>
                    <div class="info-item">
                        <strong>MIME Type:</strong>
                        <span>${evidence.mimeType}</span>
                    </div>
                    <div class="info-item">
                        <strong>Uploaded By:</strong>
                        <span>${evidence.uploader?.fullName || evidence.uploader?.username || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <strong>Upload Date:</strong>
                        <span>${formatDate(evidence.createdAt)}</span>
                    </div>
                    <div class="info-item">
                        <strong>Verification Status:</strong>
                        <span class="verification-status ${evidence.isVerified ? 'verified' : 'unverified'}">
                            ${evidence.isVerified ? 
                                '<i class="fas fa-check-circle"></i> Verified' : 
                                '<i class="fas fa-clock"></i> Unverified'
                            }
                        </span>
                    </div>
                    ${evidence.blockchainTxHash ? `
                        <div class="info-item">
                            <strong>Blockchain TX:</strong>
                            <span class="hash-value" title="${evidence.blockchainTxHash}">${evidence.blockchainTxHash.substr(0, 16)}...</span>
                            <button class="btn-copy" onclick="copyToClipboard('${evidence.blockchainTxHash}')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    ` : ''}
                </div>
                
                <div class="evidence-description-full">
                    <strong>Description:</strong>
                    <p>${escapeHtml(evidence.description)}</p>
                </div>
                
                ${evidence.tags && evidence.tags.length > 0 ? `
                    <div class="evidence-tags">
                        <strong>Tags:</strong>
                        ${evidence.tags.map(tag => `<span class="tag">${escapeHtml(tag)}</span>`).join('')}
                    </div>
                ` : ''}
                
                <div class="evidence-modal-actions">
                    <button class="btn btn-secondary" onclick="evidenceManager.downloadEvidence('${evidence._id}', '${escapeHtml(evidence.originalName)}')">
                        <i class="fas fa-download"></i> Download
                    </button>
                    <button class="btn btn-success" onclick="evidenceManager.verifyEvidence('${evidence._id}')">
                        <i class="fas fa-check"></i> Verify Integrity
                    </button>
                </div>
            </div>
        `;

        showModal('Evidence Details', modalContent);
    }

    async downloadEvidence(evidenceId, fileName) {
        try {
            showLoading();
            
            const response = await api.downloadEvidence(evidenceId);
            const blob = await response.blob();
            
            downloadBlob(blob, fileName);
            showToast('Download started', 'success');
        } catch (error) {
            console.error('Download error:', error);
            showToast('Download failed', 'error');
        } finally {
            hideLoading();
        }
    }

    async verifyEvidence(evidenceId) {
        if (!window.authManager || !window.authManager.canVerifyEvidence()) {
            showToast('You do not have permission to verify evidence', 'error');
            return;
        }

        try {
            showLoading();
            
            const response = await api.verifyEvidence(evidenceId);
            
            if (response.success) {
                const verification = response.verification;
                const status = verification.isValid ? 'success' : 'error';
                const message = verification.isValid ? 
                    'Evidence integrity verified successfully!' : 
                    'Evidence integrity verification failed!';
                
                showToast(message, status);
                
                // Refresh evidence list
                this.loadEvidences(this.currentPage);
            } else {
                showToast('Verification failed', 'error');
            }
        } catch (error) {
            console.error('Verification error:', error);
            showToast('Error during verification', 'error');
        } finally {
            hideLoading();
        }
    }
}

// Create global evidence manager instance
const evidenceManager = new EvidenceManager();
