const { Web3 } = require('web3');
const EvidenceProtectionArtifact = require('../../build/contracts/EvidenceProtection.json');

class BlockchainService {
  constructor() {
    this.web3 = null;
    this.contract = null;
    this.accounts = [];
    this.isInitialized = false;
    this.contractAddress = null;
  }

  async initialize() {
    try {
      // Connect to Ganache instance
      const rpcUrl = process.env.RPC_URL || 'http://localhost:7545';
      this.web3 = new Web3(rpcUrl);

      // Check connection
      const isConnected = await this.web3.eth.net.isListening();
      if (!isConnected) {
        throw new Error('Unable to connect to blockchain network');
      }

      // Get accounts
      this.accounts = await this.web3.eth.getAccounts();
      console.log('Connected to blockchain with', this.accounts.length, 'accounts');

      // Get contract address from deployment
      const networkId = await this.web3.eth.net.getId();
      const deployedNetwork = EvidenceProtectionArtifact.networks[networkId];

      if (!deployedNetwork) {
        throw new Error(`Contract not deployed on network ${networkId}`);
      }

      this.contractAddress = deployedNetwork.address;

      // Initialize contract instance
      this.contract = new this.web3.eth.Contract(
        EvidenceProtectionArtifact.abi,
        this.contractAddress
      );

      console.log('Contract initialized at address:', this.contractAddress);

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Blockchain initialization error:', error);
      this.isInitialized = false;
      return false;
    }
  }

  async uploadEvidence(fileHash, fileName, description, caseId, uploaderAddress) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const result = await this.contract.methods.uploadEvidence(
        fileHash,
        fileName,
        description,
        caseId
      ).send({
        from: uploaderAddress,
        gas: 500000
      });

      return {
        success: true,
        transactionHash: result.transactionHash,
        blockNumber: result.blockNumber,
        gasUsed: result.gasUsed,
        evidenceId: result.events.EvidenceUploaded.returnValues.evidenceId
      };
    } catch (error) {
      console.error('Upload evidence error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async verifyEvidence(evidenceId, fileHash, verifierAddress) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const isValid = await this.contract.methods.verifyEvidence(evidenceId, fileHash).call({
        from: verifierAddress
      });

      return {
        success: true,
        isValid: isValid
      };
    } catch (error) {
      console.error('Verify evidence error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getEvidence(evidenceId, accessorAddress) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const evidence = await this.contract.methods.getEvidence(evidenceId).call({
        from: accessorAddress
      });

      return {
        success: true,
        evidence: {
          id: parseInt(evidence.id),
          fileHash: evidence.fileHash,
          fileName: evidence.fileName,
          description: evidence.description,
          uploader: evidence.uploader,
          timestamp: new Date(parseInt(evidence.timestamp) * 1000),
          caseId: evidence.caseId,
          isActive: evidence.isActive,
          accessLog: evidence.accessLog
        }
      };
    } catch (error) {
      console.error('Get evidence error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getEvidenceByCase(caseId, accessorAddress) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const evidenceIds = await this.contract.methods.getEvidenceByCase(caseId).call({
        from: accessorAddress
      });

      return {
        success: true,
        evidenceIds: evidenceIds.map(id => parseInt(id))
      };
    } catch (error) {
      console.error('Get evidence by case error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async grantPoliceRole(userAddress, adminAddress) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const result = await this.contract.methods.grantPoliceRole(userAddress).send({
        from: adminAddress,
        gas: 100000
      });

      return {
        success: true,
        transactionHash: result.transactionHash
      };
    } catch (error) {
      console.error('Grant police role error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async grantLawyerRole(userAddress, adminAddress) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const result = await this.contract.methods.grantLawyerRole(userAddress).send({
        from: adminAddress,
        gas: 100000
      });

      return {
        success: true,
        transactionHash: result.transactionHash
      };
    } catch (error) {
      console.error('Grant lawyer role error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getTotalEvidences() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const total = await this.contract.methods.getTotalEvidences().call();
      return {
        success: true,
        total: parseInt(total)
      };
    } catch (error) {
      console.error('Get total evidences error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  getWeb3() {
    return this.web3;
  }

  getAccounts() {
    return this.accounts;
  }

  isConnected() {
    return this.isInitialized;
  }
}

// Create singleton instance
const blockchainService = new BlockchainService();

module.exports = blockchainService;
