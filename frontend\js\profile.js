// Profile Management
class ProfileManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupProfileActions();
    }

    setupProfileActions() {
        const changePasswordBtn = document.getElementById('change-password-btn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', () => this.showChangePasswordModal());
        }
    }

    loadProfile() {
        const user = window.authManager ? window.authManager.getCurrentUser() : null;
        if (!user) return;

        const profileInfo = document.getElementById('profile-info');
        if (!profileInfo) return;

        const profileHTML = `
            <div class="profile-card">
                <div class="profile-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="profile-details">
                    <div class="info-row">
                        <label>Full Name:</label>
                        <span>${user.fullName || `${user.firstName} ${user.lastName}`}</span>
                    </div>
                    <div class="info-row">
                        <label>Username:</label>
                        <span>${user.username}</span>
                    </div>
                    <div class="info-row">
                        <label>Email:</label>
                        <span>${user.email}</span>
                    </div>
                    <div class="info-row">
                        <label>Role:</label>
                        <span class="role-badge role-${user.role}">${user.role.toUpperCase()}</span>
                    </div>
                    ${user.badgeNumber ? `
                        <div class="info-row">
                            <label>Badge Number:</label>
                            <span>${user.badgeNumber}</span>
                        </div>
                    ` : ''}
                    ${user.barNumber ? `
                        <div class="info-row">
                            <label>Bar Number:</label>
                            <span>${user.barNumber}</span>
                        </div>
                    ` : ''}
                    ${user.department ? `
                        <div class="info-row">
                            <label>Department:</label>
                            <span>${user.department}</span>
                        </div>
                    ` : ''}
                    ${user.walletAddress ? `
                        <div class="info-row">
                            <label>Wallet Address:</label>
                            <span class="wallet-address" title="${user.walletAddress}">
                                ${formatAddress(user.walletAddress)}
                                <button class="btn-copy" onclick="copyToClipboard('${user.walletAddress}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </span>
                        </div>
                    ` : ''}
                    <div class="info-row">
                        <label>Member Since:</label>
                        <span>${formatDate(user.createdAt)}</span>
                    </div>
                    ${user.lastLogin ? `
                        <div class="info-row">
                            <label>Last Login:</label>
                            <span>${formatDate(user.lastLogin)}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        profileInfo.innerHTML = profileHTML;
    }

    showChangePasswordModal() {
        const modalContent = `
            <form id="change-password-form">
                <div class="form-group">
                    <label for="current-password">Current Password</label>
                    <input type="password" id="current-password" name="currentPassword" required>
                </div>
                <div class="form-group">
                    <label for="new-password">New Password</label>
                    <input type="password" id="new-password" name="newPassword" required minlength="6">
                    <small>Password must be at least 6 characters long</small>
                </div>
                <div class="form-group">
                    <label for="confirm-password">Confirm New Password</label>
                    <input type="password" id="confirm-password" name="confirmPassword" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Change Password</button>
                </div>
            </form>
        `;

        showModal('Change Password', modalContent);

        // Setup form submission
        const form = document.getElementById('change-password-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handlePasswordChange(e));
        }
    }

    async handlePasswordChange(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const currentPassword = formData.get('currentPassword');
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');

        // Validate passwords
        if (newPassword !== confirmPassword) {
            showToast('New passwords do not match', 'error');
            return;
        }

        if (newPassword.length < 6) {
            showToast('New password must be at least 6 characters long', 'error');
            return;
        }

        try {
            showLoading();

            const response = await api.changePassword({
                currentPassword,
                newPassword
            });

            if (response.success) {
                showToast('Password changed successfully', 'success');
                hideModal();
            } else {
                showToast(response.message || 'Failed to change password', 'error');
            }
        } catch (error) {
            console.error('Password change error:', error);
            showToast(error.message || 'Error changing password', 'error');
        } finally {
            hideLoading();
        }
    }

    showEditProfileModal() {
        const user = window.authManager ? window.authManager.getCurrentUser() : null;
        if (!user) return;

        const modalContent = `
            <form id="edit-profile-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="edit-first-name">First Name</label>
                        <input type="text" id="edit-first-name" name="firstName" value="${user.firstName || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-last-name">Last Name</label>
                        <input type="text" id="edit-last-name" name="lastName" value="${user.lastName || ''}" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="edit-department">Department</label>
                    <input type="text" id="edit-department" name="department" value="${user.department || ''}">
                </div>
                <div class="form-group">
                    <label for="edit-wallet-address">Wallet Address</label>
                    <input type="text" id="edit-wallet-address" name="walletAddress" value="${user.walletAddress || ''}" 
                           pattern="^0x[a-fA-F0-9]{40}$" title="Please enter a valid Ethereum address">
                    <small>Enter your Ethereum wallet address for blockchain interactions</small>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Profile</button>
                </div>
            </form>
        `;

        showModal('Edit Profile', modalContent);

        // Setup form submission
        const form = document.getElementById('edit-profile-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleProfileUpdate(e));
        }
    }

    async handleProfileUpdate(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const profileData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            department: formData.get('department'),
            walletAddress: formData.get('walletAddress')
        };

        // Remove empty fields
        Object.keys(profileData).forEach(key => {
            if (!profileData[key]) {
                delete profileData[key];
            }
        });

        try {
            showLoading();

            const response = await api.updateProfile(profileData);

            if (response.success) {
                // Update current user data
                if (window.authManager) {
                    window.authManager.currentUser = response.user;
                    window.authManager.updateUserInterface();
                }
                
                showToast('Profile updated successfully', 'success');
                hideModal();
                this.loadProfile(); // Refresh profile display
            } else {
                showToast(response.message || 'Failed to update profile', 'error');
            }
        } catch (error) {
            console.error('Profile update error:', error);
            showToast(error.message || 'Error updating profile', 'error');
        } finally {
            hideLoading();
        }
    }

    async loadUserStats() {
        const user = window.authManager ? window.authManager.getCurrentUser() : null;
        if (!user) return;

        try {
            // Get user's evidence statistics
            const response = await api.getEvidences({ uploader: user._id, limit: 1000 });
            
            if (response.success) {
                const evidences = response.evidences;
                const stats = {
                    totalUploaded: evidences.length,
                    verified: evidences.filter(e => e.isVerified).length,
                    recentUploads: evidences.filter(e => {
                        const uploadDate = new Date(e.createdAt);
                        const thirtyDaysAgo = new Date();
                        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                        return uploadDate > thirtyDaysAgo;
                    }).length
                };

                this.displayUserStats(stats);
            }
        } catch (error) {
            console.error('Error loading user stats:', error);
        }
    }

    displayUserStats(stats) {
        const profileInfo = document.getElementById('profile-info');
        if (!profileInfo) return;

        const statsHTML = `
            <div class="user-stats">
                <h3>Your Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${stats.totalUploaded}</div>
                        <div class="stat-label">Total Evidence Uploaded</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.verified}</div>
                        <div class="stat-label">Verified Evidence</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.recentUploads}</div>
                        <div class="stat-label">Recent Uploads (30 days)</div>
                    </div>
                </div>
            </div>
        `;

        profileInfo.insertAdjacentHTML('beforeend', statsHTML);
    }
}

// Create global profile manager instance
const profileManager = new ProfileManager();
