# Evidence Protection System - Deployment Guide

## Overview

This guide covers deploying the Evidence Protection System in production environments, including security considerations, performance optimization, and maintenance procedures.

## Production Requirements

### System Requirements

**Minimum Requirements:**
- CPU: 4 cores, 2.5GHz
- RAM: 8GB
- Storage: 500GB SSD
- Network: 100Mbps

**Recommended Requirements:**
- CPU: 8 cores, 3.0GHz
- RAM: 16GB
- Storage: 1TB SSD (with backup)
- Network: 1Gbps

### Software Requirements

- **Operating System**: Ubuntu 20.04 LTS or CentOS 8
- **Node.js**: v18 or higher
- **MongoDB**: v5.0 or higher
- **Nginx**: v1.18 or higher (reverse proxy)
- **SSL Certificate**: Valid SSL certificate
- **Docker**: v20.10 or higher (optional)

## Deployment Options

### Option 1: Traditional Server Deployment

#### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Install Nginx
sudo apt install nginx -y

# Install PM2 (Process Manager)
sudo npm install -g pm2
```

#### 2. Application Deployment

```bash
# Clone repository
git clone <repository-url> /opt/evidence-protection
cd /opt/evidence-protection

# Install dependencies
npm install --production

# Create production environment file
cp .env.example .env.production
```

#### 3. Environment Configuration

Edit `/opt/evidence-protection/.env.production`:

```env
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://localhost:27017/evidence_protection_prod
JWT_SECRET=your_super_secure_production_secret_key_here
JWT_EXPIRE=24h

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/evidence-protection.crt
SSL_KEY_PATH=/etc/ssl/private/evidence-protection.key

# File Upload
MAX_FILE_SIZE=100MB
UPLOAD_PATH=/var/evidence-uploads

# Security
BCRYPT_ROUNDS=14
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=50

# Blockchain (Production Network)
BLOCKCHAIN_NETWORK=mainnet
INFURA_PROJECT_ID=your_infura_project_id
PRIVATE_KEY=your_deployment_private_key
```

#### 4. Database Setup

```bash
# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Create database user
mongo
> use evidence_protection_prod
> db.createUser({
    user: "evidence_user",
    pwd: "secure_password_here",
    roles: [{ role: "readWrite", db: "evidence_protection_prod" }]
  })
> exit

# Update MongoDB configuration for authentication
sudo nano /etc/mongod.conf
```

Add to `/etc/mongod.conf`:
```yaml
security:
  authorization: enabled
```

#### 5. SSL Certificate Setup

```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# Or install custom certificate
sudo cp your-certificate.crt /etc/ssl/certs/evidence-protection.crt
sudo cp your-private-key.key /etc/ssl/private/evidence-protection.key
sudo chmod 600 /etc/ssl/private/evidence-protection.key
```

#### 6. Nginx Configuration

Create `/etc/nginx/sites-available/evidence-protection`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/evidence-protection.crt;
    ssl_certificate_key /etc/ssl/private/evidence-protection.key;
    
    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # File Upload Size
    client_max_body_size 100M;

    # Static Files
    location /uploads {
        alias /var/evidence-uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API Proxy
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Frontend
    location / {
        root /opt/evidence-protection/frontend;
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public";
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/evidence-protection /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

#### 7. Application Startup

```bash
# Create uploads directory
sudo mkdir -p /var/evidence-uploads
sudo chown -R www-data:www-data /var/evidence-uploads

# Start application with PM2
cd /opt/evidence-protection
pm2 start backend/server.js --name evidence-protection --env production
pm2 save
pm2 startup
```

### Option 2: Docker Deployment

#### 1. Create Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Expose port
EXPOSE 3000

# Start application
CMD ["node", "backend/server.js"]
```

#### 2. Create docker-compose.yml

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/evidence_protection
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - mongo
      - ganache
    restart: unless-stopped

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
      - MONGO_INITDB_DATABASE=evidence_protection
    volumes:
      - mongo_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    restart: unless-stopped

  ganache:
    image: trufflesuite/ganache-cli:latest
    ports:
      - "8545:8545"
    command: >
      --deterministic
      --accounts 10
      --host 0.0.0.0
      --port 8545
      --networkId 5777
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl:ro
      - ./frontend:/usr/share/nginx/html:ro
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongo_data:
```

#### 3. Deploy with Docker

```bash
# Create environment file
echo "JWT_SECRET=your_production_secret" > .env
echo "MONGO_PASSWORD=secure_mongo_password" >> .env

# Build and start services
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs app
```

## Security Configuration

### 1. Firewall Setup

```bash
# UFW (Ubuntu)
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# Fail2ban (brute force protection)
sudo apt install fail2ban
sudo systemctl enable fail2ban
```

### 2. MongoDB Security

```bash
# Enable authentication
sudo nano /etc/mongod.conf
```

Add:
```yaml
security:
  authorization: enabled
net:
  bindIp: 127.0.0.1
```

### 3. Application Security

- Use strong JWT secrets (minimum 256 bits)
- Enable HTTPS only
- Implement proper CORS policies
- Regular security updates
- Monitor access logs

## Performance Optimization

### 1. Database Optimization

```javascript
// Create indexes for better performance
db.evidences.createIndex({ "caseId": 1 })
db.evidences.createIndex({ "fileHash": 1 })
db.evidences.createIndex({ "uploader": 1, "createdAt": -1 })
db.evidences.createIndex({ "createdAt": -1 })
db.users.createIndex({ "username": 1 })
db.users.createIndex({ "email": 1 })
```

### 2. Application Optimization

```javascript
// PM2 cluster mode
pm2 start backend/server.js --name evidence-protection -i max --env production
```

### 3. Nginx Optimization

Add to nginx configuration:
```nginx
# Gzip compression
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Connection optimization
keepalive_timeout 65;
keepalive_requests 100;
```

## Monitoring and Logging

### 1. Application Monitoring

```bash
# PM2 monitoring
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30

# System monitoring
sudo apt install htop iotop nethogs
```

### 2. Log Management

```javascript
// Winston logger configuration
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});
```

### 3. Health Checks

```bash
# Create health check script
cat > /opt/evidence-protection/health-check.sh << 'EOF'
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/health)
if [ $response -eq 200 ]; then
    echo "Application is healthy"
    exit 0
else
    echo "Application is unhealthy (HTTP $response)"
    exit 1
fi
EOF

chmod +x /opt/evidence-protection/health-check.sh

# Add to crontab for monitoring
echo "*/5 * * * * /opt/evidence-protection/health-check.sh" | crontab -
```

## Backup and Recovery

### 1. Database Backup

```bash
# Create backup script
cat > /opt/evidence-protection/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups"
mkdir -p $BACKUP_DIR

# MongoDB backup
mongodump --db evidence_protection_prod --out $BACKUP_DIR/mongo_$DATE

# Application files backup
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /opt/evidence-protection/uploads

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "mongo_*" -mtime +30 -delete
find $BACKUP_DIR -name "app_*" -mtime +30 -delete
EOF

chmod +x /opt/evidence-protection/backup.sh

# Schedule daily backups
echo "0 2 * * * /opt/evidence-protection/backup.sh" | crontab -
```

### 2. Recovery Procedures

```bash
# Restore MongoDB
mongorestore --db evidence_protection_prod /path/to/backup/evidence_protection_prod

# Restore application files
tar -xzf /path/to/backup/app_backup.tar.gz -C /
```

## Maintenance

### 1. Regular Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js dependencies
cd /opt/evidence-protection
npm audit fix
npm update

# Restart application
pm2 restart evidence-protection
```

### 2. Log Rotation

```bash
# Configure logrotate
sudo nano /etc/logrotate.d/evidence-protection
```

Add:
```
/opt/evidence-protection/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload evidence-protection
    endscript
}
```

### 3. Certificate Renewal

```bash
# Auto-renew Let's Encrypt certificates
sudo crontab -e
```

Add:
```
0 12 * * * /usr/bin/certbot renew --quiet
```

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Check logs: `pm2 logs evidence-protection`
   - Verify environment variables
   - Check database connection

2. **High memory usage**
   - Monitor with `pm2 monit`
   - Check for memory leaks
   - Restart application if needed

3. **Database connection issues**
   - Check MongoDB status: `sudo systemctl status mongod`
   - Verify authentication credentials
   - Check network connectivity

4. **SSL certificate issues**
   - Verify certificate validity: `openssl x509 -in cert.crt -text -noout`
   - Check nginx configuration
   - Restart nginx: `sudo systemctl restart nginx`

### Emergency Procedures

1. **Application crash**
   ```bash
   pm2 restart evidence-protection
   pm2 logs evidence-protection --lines 100
   ```

2. **Database corruption**
   ```bash
   mongod --repair
   # Restore from backup if needed
   ```

3. **Disk space full**
   ```bash
   # Clean old logs
   pm2 flush
   # Clean old backups
   find /opt/backups -mtime +7 -delete
   ```

This deployment guide provides a comprehensive approach to deploying the Evidence Protection System in production environments with proper security, monitoring, and maintenance procedures.
