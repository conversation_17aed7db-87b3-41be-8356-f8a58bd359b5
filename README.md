# Evidence Protection System

A comprehensive blockchain-based digital evidence management system designed for law enforcement and legal professionals. This system ensures evidence integrity through cryptographic hashing and immutable blockchain storage using Ethereum smart contracts.

![Evidence Protection System](https://img.shields.io/badge/Status-Production%20Ready-green)
![Node.js](https://img.shields.io/badge/Node.js-18+-green)
![MongoDB](https://img.shields.io/badge/MongoDB-5.0+-green)
![Solidity](https://img.shields.io/badge/Solidity-0.8.19-blue)

## 🚀 Features

### Core Functionality
- **🔗 Blockchain Integration**: Ethereum-based smart contracts for immutable evidence storage
- **👥 User Authentication**: Role-based access control for authorized personnel
- **🔐 File Integrity**: SHA-256 cryptographic hashing for evidence verification
- **📁 Evidence Management**: Upload, view, download, and verify digital evidence
- **📊 Audit Trail**: Complete blockchain-based audit trail for all evidence interactions
- **🔍 Evidence Verification**: Real-time integrity checking with blockchain validation

### Security Features
- **🛡️ Role-Based Access Control**: Police, Lawyers, and Administrators with specific permissions
- **🔑 JWT Authentication**: Secure token-based authentication system
- **🔒 File Encryption**: Secure evidence storage with cryptographic protection
- **📝 Comprehensive Logging**: All access and modifications are logged immutably
- **⚡ Rate Limiting**: Protection against brute force and DDoS attacks
- **🌐 HTTPS Support**: SSL/TLS encryption for all communications

### User Experience
- **📱 Responsive Design**: Works on desktop, tablet, and mobile devices
- **⚡ Real-time Updates**: Live notifications and status updates
- **🎨 Modern UI**: Clean, intuitive interface designed for professionals
- **🔄 File Preview**: Support for images, videos, audio, and documents
- **📋 Advanced Filtering**: Search and filter evidence by case, date, status, etc.

## 🛠️ Technology Stack

### Frontend
- **HTML5/CSS3**: Modern web standards with responsive design
- **JavaScript (ES6+)**: Vanilla JavaScript for optimal performance
- **Font Awesome**: Professional iconography
- **CSS Grid/Flexbox**: Advanced layout systems

### Backend
- **Node.js**: JavaScript runtime for server-side development
- **Express.js**: Fast, unopinionated web framework
- **MongoDB**: NoSQL database for flexible data storage
- **Mongoose**: Elegant MongoDB object modeling
- **JWT**: JSON Web Tokens for secure authentication
- **Multer**: Middleware for handling file uploads
- **bcrypt**: Password hashing for security

### Blockchain
- **Ethereum**: Decentralized blockchain platform
- **Solidity**: Smart contract programming language
- **Truffle**: Development framework for Ethereum
- **Ganache**: Personal blockchain for development
- **Web3.js**: Ethereum JavaScript API
- **OpenZeppelin**: Secure smart contract library

### Security & DevOps
- **Helmet**: Security middleware for Express
- **CORS**: Cross-Origin Resource Sharing configuration
- **Rate Limiting**: Request throttling and protection
- **PM2**: Production process manager
- **Nginx**: Reverse proxy and load balancer
- **Docker**: Containerization support

## 📁 Project Structure

```
evidence-protection-system/
├── 📂 backend/                 # Node.js backend API
│   ├── 📂 models/              # Database models (User, Evidence)
│   ├── 📂 routes/              # API routes (auth, evidence)
│   ├── 📂 middleware/          # Express middleware (auth, validation)
│   ├── 📂 utils/               # Utility functions (blockchain, file handling)
│   └── 📄 server.js            # Main server file
├── 📂 frontend/                # Web frontend
│   ├── 📂 css/                 # Stylesheets
│   ├── 📂 js/                  # JavaScript modules
│   └── 📄 index.html           # Main HTML file
├── 📂 contracts/               # Solidity smart contracts
│   ├── 📄 EvidenceProtection.sol # Main evidence contract
│   └── 📄 Migrations.sol       # Migration contract
├── 📂 migrations/              # Truffle migration scripts
├── 📂 test/                    # Test files
├── 📂 config/                  # Configuration files
├── 📂 docs/                    # Comprehensive documentation
├── 📂 scripts/                 # Utility scripts
├── 📂 uploads/                 # Evidence file storage
└── 📄 package.json             # Project dependencies
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (v5.0 or higher)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd EVIDENCE_PROT
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start MongoDB**
   ```bash
   # Ensure MongoDB is running on your system
   ```

5. **Start Ganache blockchain**
   ```bash
   npx ganache-cli --deterministic --accounts 10 --host 127.0.0.1 --port 8545
   ```

6. **Deploy smart contracts**
   ```bash
   npm run compile
   npm run migrate
   ```

7. **Create test users**
   ```bash
   node scripts/createTestUsers.js
   ```

8. **Start the application**
   ```bash
   npm run dev
   ```

9. **Access the system**
   - Open your browser to `http://localhost:9999`
   - Login with test credentials:
     - **Admin**: `admin` / `admin123`
     - **Police**: `officer1` / `police123`
     - **Lawyer**: `lawyer1` / `lawyer123`

## 👥 User Roles & Permissions

### 🔧 Administrator
- Create and manage user accounts
- Grant blockchain roles to users
- Access all system features and evidence
- View system statistics and audit logs
- Configure system settings

### 👮 Police Officer
- Upload evidence files to the blockchain
- View and download evidence
- Verify evidence integrity
- Access evidence by case ID
- View personal upload statistics

### ⚖️ Lawyer
- View and download evidence
- Verify evidence integrity
- Access evidence by case ID
- Generate verification reports
- **Cannot upload new evidence**

## 📚 Documentation

- **[Setup Guide](docs/SETUP_GUIDE.md)**: Detailed installation and configuration
- **[User Manual](docs/USER_MANUAL.md)**: Complete user guide for all roles
- **[API Documentation](docs/API_DOCUMENTATION.md)**: REST API reference
- **[Deployment Guide](docs/DEPLOYMENT_GUIDE.md)**: Production deployment instructions

## 🔒 Security Features

### Authentication & Authorization
- JWT-based secure authentication
- Role-based access control (RBAC)
- Session management and timeout
- Password strength requirements
- Account lockout protection

### Data Protection
- SHA-256 file hashing for integrity
- Blockchain immutable storage
- Encrypted data transmission (HTTPS)
- Secure file upload validation
- Access logging and audit trails

### Blockchain Security
- Smart contract access control
- Immutable evidence records
- Cryptographic proof of integrity
- Decentralized verification
- Tamper-evident storage

## 🧪 Testing

```bash
# Run all tests
npm test

# Run specific test suites
npm test -- --grep "Evidence"
npm test -- --grep "Authentication"

# Run with coverage
npm run test:coverage
```

## 🚀 Production Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d

# Check status
docker-compose ps
```

### Traditional Server Deployment
```bash
# Install PM2 globally
npm install -g pm2

# Start in production mode
NODE_ENV=production pm2 start backend/server.js --name evidence-protection

# Save PM2 configuration
pm2 save
pm2 startup
```

See the [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) for detailed production setup instructions.

## 📊 System Requirements

### Development
- **CPU**: 2 cores, 2.0GHz
- **RAM**: 4GB
- **Storage**: 100GB
- **Network**: Broadband internet

### Production
- **CPU**: 4+ cores, 2.5GHz
- **RAM**: 8GB+ (16GB recommended)
- **Storage**: 500GB+ SSD
- **Network**: 100Mbps+ (1Gbps recommended)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs](docs/) directory
- **Issues**: Report bugs via GitHub Issues
- **Security**: Report security issues privately to the maintainers

## 🙏 Acknowledgments

- **OpenZeppelin**: For secure smart contract libraries
- **Truffle Suite**: For blockchain development tools
- **MongoDB**: For flexible data storage
- **Express.js**: For robust web framework
- **Font Awesome**: For professional icons

---

**⚠️ Important**: This system handles sensitive legal evidence. Ensure proper security measures, regular backups, and compliance with local laws and regulations before deploying in production.
