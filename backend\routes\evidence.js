const express = require('express');
const path = require('path');
const Evidence = require('../models/Evidence');
const { authenticateToken, requirePolice, requireEvidenceAccess } = require('../middleware/auth');
const { upload, calculateFileHash, verifyFileIntegrity, validateEvidenceFile, getFileMetadata } = require('../utils/fileHandler');
const blockchainService = require('../utils/blockchain');

const router = express.Router();

// @route   POST /api/evidence/upload
// @desc    Upload new evidence
// @access  Private (Police only)
router.post('/upload', authenticateToken, requirePolice, upload.single('evidenceFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const { description, caseId, tags } = req.body;

    if (!description || !caseId) {
      return res.status(400).json({
        success: false,
        message: 'Description and case ID are required'
      });
    }

    // Validate file
    const validation = validateEvidenceFile(req.file);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'File validation failed',
        errors: validation.errors
      });
    }

    // Calculate file hash
    const fileHash = await calculateFileHash(req.file.path);

    // Check if evidence with this hash already exists
    const existingEvidence = await Evidence.findOne({ fileHash });
    if (existingEvidence) {
      return res.status(400).json({
        success: false,
        message: 'Evidence with this file hash already exists',
        existingEvidenceId: existingEvidence.evidenceId
      });
    }

    // Get file metadata
    const metadata = await getFileMetadata(req.file.path);

    // Upload to blockchain
    let blockchainResult = null;
    try {
      if (req.user.walletAddress && blockchainService.isConnected()) {
        blockchainResult = await blockchainService.uploadEvidence(
          fileHash,
          req.file.originalname,
          description,
          caseId,
          req.user.walletAddress
        );
      } else {
        console.log('⚠️ Blockchain upload skipped: No wallet address or blockchain not connected');
      }
    } catch (blockchainError) {
      console.error('⚠️ Blockchain upload failed:', blockchainError.message);
      // Continue with database upload even if blockchain fails
    }

    // Create evidence record in database
    const evidence = new Evidence({
      evidenceId: blockchainResult?.evidenceId || Date.now(),
      fileName: req.file.filename,
      originalName: req.file.originalname,
      fileHash,
      filePath: req.file.path,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      description,
      caseId,
      uploader: req.user._id,
      uploaderAddress: req.user.walletAddress || null,
      blockchainTxHash: blockchainResult?.transactionHash || null,
      blockNumber: blockchainResult?.blockNumber || null,
      gasUsed: blockchainResult?.gasUsed || null,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      isVerified: blockchainResult?.success || false
    });

    await evidence.save();

    // Add access log
    await evidence.addAccessLog(req.user, 'upload', req.ip, req.get('User-Agent'));

    res.status(201).json({
      success: true,
      message: 'Evidence uploaded successfully',
      evidence: {
        id: evidence._id,
        evidenceId: evidence.evidenceId,
        fileName: evidence.originalName,
        fileHash: evidence.fileHash,
        description: evidence.description,
        caseId: evidence.caseId,
        uploadedAt: evidence.createdAt,
        blockchainTxHash: evidence.blockchainTxHash,
        isVerified: evidence.isVerified
      }
    });
  } catch (error) {
    console.error('Evidence upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during evidence upload'
    });
  }
});

// @route   GET /api/evidence/unverified
// @desc    Get all unverified evidence for verification page
// @access  Private (Police, Lawyer, Admin)
router.get('/unverified', authenticateToken, requireEvidenceAccess, async (req, res) => {
  try {
    const unverifiedEvidence = await Evidence.find({
      isVerified: false,
      isActive: true
    })
      .populate('uploader', 'username firstName lastName role')
      .sort({ uploadedAt: -1 })
      .limit(50); // Limit to 50 most recent unverified items

    res.json({
      success: true,
      evidence: unverifiedEvidence,
      count: unverifiedEvidence.length
    });
  } catch (error) {
    console.error('Get unverified evidence error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching unverified evidence'
    });
  }
});

// @route   GET /api/evidence
// @desc    Get all evidence (with pagination and filtering)
// @access  Private (Police, Lawyer, Admin)
router.get('/', authenticateToken, requireEvidenceAccess, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = { isActive: true };
    
    if (req.query.caseId) {
      filter.caseId = req.query.caseId;
    }
    
    if (req.query.uploader) {
      filter.uploader = req.query.uploader;
    }
    
    if (req.query.verified !== undefined) {
      filter.isVerified = req.query.verified === 'true';
    }

    // Get evidences with pagination
    const evidences = await Evidence.find(filter)
      .populate('uploader', 'username firstName lastName role')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Evidence.countDocuments(filter);

    res.json({
      success: true,
      evidences,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get evidences error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching evidences'
    });
  }
});

// @route   GET /api/evidence/:id
// @desc    Get specific evidence
// @access  Private (Police, Lawyer, Admin)
router.get('/:id', authenticateToken, requireEvidenceAccess, async (req, res) => {
  try {
    // Try to find by MongoDB _id first, then by evidenceId
    let evidence;
    if (req.params.id.match(/^[0-9a-fA-F]{24}$/)) {
      // It's a valid MongoDB ObjectId
      evidence = await Evidence.findById(req.params.id)
        .populate('uploader', 'username firstName lastName role')
        .populate('accessLog.accessor', 'username firstName lastName role')
        .populate('verificationAttempts.verifier', 'username firstName lastName role');
    } else {
      // It's likely an evidenceId
      evidence = await Evidence.findOne({ evidenceId: req.params.id })
        .populate('uploader', 'username firstName lastName role')
        .populate('accessLog.accessor', 'username firstName lastName role')
        .populate('verificationAttempts.verifier', 'username firstName lastName role');
    }

    if (!evidence) {
      return res.status(404).json({
        success: false,
        message: 'Evidence not found'
      });
    }

    if (!evidence.isActive) {
      return res.status(403).json({
        success: false,
        message: 'Evidence is not active'
      });
    }

    // Add access log
    await evidence.addAccessLog(req.user, 'view', req.ip, req.get('User-Agent'));

    res.json({
      success: true,
      evidence
    });
  } catch (error) {
    console.error('Get evidence error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching evidence'
    });
  }
});

// @route   POST /api/evidence/:id/verify
// @desc    Verify evidence integrity
// @access  Private (Police, Lawyer, Admin)
router.post('/:id/verify', authenticateToken, requireEvidenceAccess, async (req, res) => {
  try {
    // Try to find by MongoDB _id first, then by evidenceId
    let evidence;
    if (req.params.id.match(/^[0-9a-fA-F]{24}$/)) {
      evidence = await Evidence.findById(req.params.id);
    } else {
      evidence = await Evidence.findOne({ evidenceId: req.params.id });
    }

    if (!evidence) {
      return res.status(404).json({
        success: false,
        message: 'Evidence not found'
      });
    }

    if (!evidence.isActive) {
      return res.status(403).json({
        success: false,
        message: 'Evidence is not active'
      });
    }

    // Verify file integrity
    const fileIntegrityValid = await verifyFileIntegrity(evidence.filePath, evidence.fileHash);

    let blockchainVerificationResult = null;

    // Verify on blockchain if connected
    try {
      if (req.user.walletAddress && blockchainService.isConnected()) {
        blockchainVerificationResult = await blockchainService.verifyEvidence(
          evidence.evidenceId,
          evidence.fileHash,
          req.user.walletAddress
        );
      } else {
        console.log('⚠️ Blockchain verification skipped: No wallet address or blockchain not connected');
      }
    } catch (blockchainError) {
      console.error('⚠️ Blockchain verification failed:', blockchainError.message);
      // Continue with file verification even if blockchain fails
    }

    const isValid = fileIntegrityValid && (blockchainVerificationResult?.isValid !== false);

    // Add verification attempt
    await evidence.addVerificationAttempt(
      req.user,
      isValid,
      blockchainVerificationResult?.transactionHash
    );

    // Add access log
    await evidence.addAccessLog(req.user, 'verify', req.ip, req.get('User-Agent'));

    res.json({
      success: true,
      verification: {
        isValid,
        fileIntegrityValid,
        blockchainValid: blockchainVerificationResult?.isValid,
        verifiedAt: new Date(),
        verifiedBy: {
          id: req.user._id,
          username: req.user.username,
          fullName: req.user.fullName
        }
      }
    });
  } catch (error) {
    console.error('Evidence verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during evidence verification'
    });
  }
});

// @route   GET /api/evidence/:id/download
// @desc    Download evidence file
// @access  Private (Police, Lawyer, Admin)
router.get('/:id/download', authenticateToken, requireEvidenceAccess, async (req, res) => {
  try {
    const evidence = await Evidence.findById(req.params.id);

    if (!evidence) {
      return res.status(404).json({
        success: false,
        message: 'Evidence not found'
      });
    }

    if (!evidence.isActive) {
      return res.status(403).json({
        success: false,
        message: 'Evidence is not active'
      });
    }

    // Add access log
    await evidence.addAccessLog(req.user, 'download', req.ip, req.get('User-Agent'));

    // Set appropriate headers
    res.setHeader('Content-Disposition', `attachment; filename="${evidence.originalName}"`);
    res.setHeader('Content-Type', evidence.mimeType);

    // Send file
    res.sendFile(path.resolve(evidence.filePath));
  } catch (error) {
    console.error('Evidence download error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during evidence download'
    });
  }
});

// @route   GET /api/evidence/case/:caseId
// @desc    Get evidence by case ID
// @access  Private (Police, Lawyer, Admin)
router.get('/case/:caseId', authenticateToken, requireEvidenceAccess, async (req, res) => {
  try {
    const evidences = await Evidence.find({ 
      caseId: req.params.caseId, 
      isActive: true 
    })
      .populate('uploader', 'username firstName lastName role')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      caseId: req.params.caseId,
      evidences,
      count: evidences.length
    });
  } catch (error) {
    console.error('Get evidence by case error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching case evidence'
    });
  }
});

module.exports = router;
