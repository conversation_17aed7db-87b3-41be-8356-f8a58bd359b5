# Evidence Protection System - User Manual

## Table of Contents

1. [Getting Started](#getting-started)
2. [User Roles](#user-roles)
3. [Dashboard](#dashboard)
4. [Evidence Management](#evidence-management)
5. [Evidence Verification](#evidence-verification)
6. [User Profile](#user-profile)
7. [Security Features](#security-features)
8. [Troubleshooting](#troubleshooting)

## Getting Started

### Logging In

1. Open your web browser and navigate to the Evidence Protection System
2. Enter your username/email and password
3. Click "Login"

**Default Test Accounts:**
- Admin: `admin` / `admin123`
- Police Officer: `officer1` / `police123`
- Lawyer: `lawyer1` / `lawyer123`

### Navigation

The system uses a top navigation bar with the following sections:
- **Dashboard**: System overview and statistics
- **Upload Evidence**: Upload new evidence files (Police only)
- **View Evidence**: Browse and manage evidence
- **Verify Evidence**: Verify evidence integrity
- **Profile**: Manage your account settings

## User Roles

### Administrator
**Capabilities:**
- Create and manage user accounts
- Access all system features
- Grant blockchain roles to users
- View system statistics and logs
- Manage system configuration

### Police Officer
**Capabilities:**
- Upload evidence files to the system
- View and download evidence
- Verify evidence integrity
- Access evidence by case ID
- View personal upload statistics

### Lawyer
**Capabilities:**
- View and download evidence
- Verify evidence integrity
- Access evidence by case ID
- Cannot upload new evidence

## Dashboard

The dashboard provides an overview of the system status and recent activity.

### Statistics Cards
- **Total Evidence**: Number of evidence files in the system
- **Verified Evidence**: Number of blockchain-verified files
- **Recent Uploads**: Files uploaded in the last 7 days
- **Blockchain Status**: Connection status to the blockchain network

### Recent Activity
Shows the latest evidence uploads and system activities with:
- Evidence file names
- Case IDs
- Upload timestamps
- Verification status

## Evidence Management

### Uploading Evidence (Police Only)

1. Navigate to "Upload Evidence"
2. Fill in the required information:
   - **Case ID**: Unique identifier for the case
   - **Evidence File**: Select the file to upload (max 50MB)
   - **Description**: Detailed description of the evidence
   - **Tags**: Optional tags for categorization

3. Preview the file (for images, videos, and audio)
4. Click "Upload Evidence"

**Supported File Types:**
- Images: JPEG, PNG, GIF, BMP, TIFF
- Videos: MP4, AVI, MOV, WMV
- Audio: MP3, WAV, AAC
- Documents: PDF, DOC, DOCX, TXT
- Archives: ZIP, RAR

### Viewing Evidence

1. Navigate to "View Evidence"
2. Use filters to find specific evidence:
   - Search by Case ID
   - Filter by verification status
3. Click on evidence cards to view details

### Evidence Details

Each evidence file displays:
- **File Information**: Name, size, type, upload date
- **Case Information**: Case ID, description, tags
- **Security Information**: SHA-256 hash, blockchain transaction
- **Access Log**: Who accessed the file and when
- **Verification Status**: Blockchain verification status

### Downloading Evidence

1. Find the evidence file you want to download
2. Click the "Download" button
3. The file will be downloaded to your computer
4. Access is logged for audit purposes

## Evidence Verification

### Manual Verification

1. Navigate to "Verify Evidence"
2. Enter the Evidence ID
3. Click "Verify Evidence"

### Verification Results

The system performs multiple checks:
- **File Integrity**: Compares current file hash with original
- **Blockchain Verification**: Confirms evidence exists on blockchain
- **Access Verification**: Checks user permissions

### Verification Report

Results show:
- ✅ **Verified**: Evidence integrity confirmed
- ❌ **Failed**: Evidence may be compromised
- ⚠️ **Warning**: Partial verification (e.g., blockchain unavailable)

## User Profile

### Viewing Profile Information

1. Navigate to "Profile"
2. View your account details:
   - Personal information
   - Role and permissions
   - Wallet address (for blockchain)
   - Account statistics

### Changing Password

1. In the Profile section, click "Change Password"
2. Enter your current password
3. Enter and confirm your new password
4. Click "Change Password"

**Password Requirements:**
- Minimum 6 characters
- Must be different from current password

### Updating Profile

Administrators can update user profiles including:
- Personal information
- Department
- Wallet address
- Role assignments

## Security Features

### File Integrity

Every uploaded file is protected by:
- **SHA-256 Hashing**: Cryptographic fingerprint of the file
- **Blockchain Storage**: Immutable record on Ethereum
- **Access Logging**: Complete audit trail of file access

### Authentication

- **JWT Tokens**: Secure session management
- **Role-Based Access**: Different permissions for each user type
- **Rate Limiting**: Protection against brute force attacks

### Blockchain Integration

- **Smart Contracts**: Automated evidence management
- **Immutable Records**: Cannot be altered or deleted
- **Decentralized Verification**: Independent verification possible

## Best Practices

### For Police Officers

1. **Accurate Case IDs**: Use consistent case numbering
2. **Detailed Descriptions**: Provide comprehensive evidence descriptions
3. **Immediate Upload**: Upload evidence as soon as possible
4. **Proper Tagging**: Use relevant tags for easy searching
5. **Verify After Upload**: Always verify evidence after uploading

### For Lawyers

1. **Verify Before Use**: Always verify evidence before using in court
2. **Document Access**: Keep records of evidence access
3. **Check Integrity**: Regularly verify evidence integrity
4. **Secure Downloads**: Ensure downloaded files are stored securely

### For Administrators

1. **Regular Backups**: Backup database and blockchain data
2. **User Management**: Regularly review user accounts and permissions
3. **System Monitoring**: Monitor system health and performance
4. **Security Updates**: Keep system updated with latest security patches

## Error Messages and Solutions

### Common Error Messages

**"File size exceeds limit"**
- Solution: Compress file or split into smaller parts (max 50MB)

**"Invalid file type"**
- Solution: Convert to supported format or contact administrator

**"Case ID already exists"**
- Solution: Use unique case ID or append suffix

**"Verification failed"**
- Solution: File may be corrupted; re-upload if necessary

**"Access denied"**
- Solution: Contact administrator to verify permissions

**"Blockchain connection error"**
- Solution: Wait for blockchain to reconnect or contact IT support

### Network Issues

**"Connection timeout"**
- Check internet connection
- Refresh the page
- Contact IT support if problem persists

**"Server unavailable"**
- System may be under maintenance
- Try again in a few minutes
- Contact administrator if issue continues

## Data Privacy and Compliance

### Data Protection

- All evidence files are encrypted during transmission
- Access is logged and monitored
- User data is protected according to privacy regulations
- Regular security audits are performed

### Legal Compliance

- System maintains chain of custody
- All access is logged with timestamps
- Evidence integrity is cryptographically guaranteed
- Audit trails are immutable and verifiable

### Retention Policies

- Evidence files are retained according to legal requirements
- Access logs are maintained indefinitely
- User accounts can be deactivated but not deleted
- Blockchain records are permanent

## Support and Contact

### Technical Support

For technical issues:
1. Check this user manual
2. Try the troubleshooting steps
3. Contact your system administrator
4. Document error messages and steps to reproduce

### Training

New users should:
1. Complete system orientation
2. Practice with test cases
3. Understand their role permissions
4. Know emergency procedures

### Feedback

To improve the system:
- Report bugs or issues
- Suggest new features
- Provide usability feedback
- Participate in user surveys

## Appendix

### Keyboard Shortcuts

- `Ctrl + /` or `Cmd + /`: Search evidence
- `Escape`: Close modal dialogs
- `Enter`: Submit forms

### File Naming Conventions

Recommended naming format:
`CASE-ID_EVIDENCE-TYPE_DATE_SEQUENCE`

Example: `2024-001_PHOTO_20240625_001.jpg`

### Glossary

- **Hash**: Cryptographic fingerprint of a file
- **Blockchain**: Distributed ledger technology
- **Smart Contract**: Self-executing contract on blockchain
- **JWT**: JSON Web Token for authentication
- **Case ID**: Unique identifier for legal cases
- **Evidence ID**: System-generated unique identifier for evidence files
