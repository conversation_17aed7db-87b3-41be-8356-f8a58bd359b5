const mongoose = require('mongoose');

const evidenceSchema = new mongoose.Schema({
  evidenceId: {
    type: Number,
    required: true,
    unique: true
  },
  fileName: {
    type: String,
    required: [true, 'File name is required'],
    trim: true
  },
  originalName: {
    type: String,
    required: [true, 'Original file name is required'],
    trim: true
  },
  fileHash: {
    type: String,
    required: [true, 'File hash is required'],
    unique: true,
    match: [/^[a-f0-9]{64}$/, 'Invalid SHA-256 hash format']
  },
  filePath: {
    type: String,
    required: [true, 'File path is required']
  },
  fileSize: {
    type: Number,
    required: [true, 'File size is required']
  },
  mimeType: {
    type: String,
    required: [true, 'MIME type is required']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  caseId: {
    type: String,
    required: [true, 'Case ID is required'],
    trim: true,
    index: true
  },
  uploader: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Uploader is required']
  },
  uploaderAddress: {
    type: String,
    required: false,
    match: [/^0x[a-fA-F0-9]{40}$/, 'Invalid Ethereum address']
  },
  blockchainTxHash: {
    type: String,
    match: [/^0x[a-fA-F0-9]{64}$/, 'Invalid transaction hash']
  },
  blockNumber: {
    type: Number
  },
  gasUsed: {
    type: Number
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationAttempts: [{
    verifier: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    verifierAddress: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    result: Boolean,
    txHash: String
  }],
  accessLog: [{
    accessor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    accessorAddress: String,
    action: {
      type: String,
      enum: ['upload', 'view', 'download', 'verify', 'update']
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    ipAddress: String,
    userAgent: String
  }],
  tags: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index for efficient queries
evidenceSchema.index({ caseId: 1, createdAt: -1 });
evidenceSchema.index({ uploader: 1, createdAt: -1 });
evidenceSchema.index({ fileHash: 1 });
evidenceSchema.index({ evidenceId: 1 });

// Update the updatedAt field before saving
evidenceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Add access log entry
evidenceSchema.methods.addAccessLog = function(accessor, action, ipAddress, userAgent) {
  this.accessLog.push({
    accessor: accessor._id,
    accessorAddress: accessor.walletAddress,
    action: action,
    ipAddress: ipAddress,
    userAgent: userAgent
  });
  return this.save();
};

// Add verification attempt
evidenceSchema.methods.addVerificationAttempt = function(verifier, result, txHash) {
  this.verificationAttempts.push({
    verifier: verifier._id,
    verifierAddress: verifier.walletAddress,
    result: result,
    txHash: txHash
  });
  
  if (result) {
    this.isVerified = true;
  }
  
  return this.save();
};

// Get file extension
evidenceSchema.virtual('fileExtension').get(function() {
  return this.originalName.split('.').pop().toLowerCase();
});

// Ensure virtual fields are serialized
evidenceSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('Evidence', evidenceSchema);
