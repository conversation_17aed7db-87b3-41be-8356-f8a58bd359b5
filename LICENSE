MIT License

Copyright (c) 2024 Evidence Protection System

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

ADDITIONAL TERMS FOR EVIDENCE PROTECTION SYSTEM:

1. LEGAL COMPLIANCE: Users of this software are responsible for ensuring 
   compliance with all applicable laws, regulations, and legal requirements 
   in their jurisdiction regarding evidence handling, data protection, and 
   digital forensics.

2. SECURITY RESPONSIBILITY: Users must implement appropriate security measures
   including but not limited to secure hosting, regular updates, proper access
   controls, and data backup procedures.

3. AUDIT REQUIREMENTS: Organizations using this software for legal evidence
   management should maintain proper audit trails and documentation as required
   by their legal and regulatory framework.

4. NO WARRANTY FOR LEGAL PURPOSES: While this software implements security
   best practices, the authors provide no warranty regarding its suitability
   for specific legal proceedings or compliance with particular legal standards.

5. DATA PROTECTION: Users are responsible for implementing appropriate data
   protection measures and ensuring compliance with privacy regulations such
   as GDPR, CCPA, or other applicable data protection laws.

6. BLOCKCHAIN CONSIDERATIONS: Users acknowledge that blockchain transactions
   are immutable and should carefully consider the implications of storing
   evidence hashes on public or private blockchain networks.

For questions regarding licensing or legal compliance, please consult with
qualified legal counsel familiar with digital evidence and blockchain technology
in your jurisdiction.
