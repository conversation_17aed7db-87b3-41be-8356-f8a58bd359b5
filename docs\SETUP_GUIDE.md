# Evidence Protection System - Setup Guide

## Overview

The Evidence Protection System is a blockchain-based digital evidence management platform designed for law enforcement and legal professionals. It ensures evidence integrity through cryptographic hashing and immutable blockchain storage.

## Prerequisites

Before setting up the system, ensure you have the following installed:

- **Node.js** (v16 or higher)
- **MongoDB** (v4.4 or higher)
- **Git**
- **Web browser** (Chrome, Firefox, Safari, or Edge)

## Installation Steps

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd EVIDENCE_PROT
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit the `.env` file with your specific configuration:

```env
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/evidence_protection

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d

# Server Configuration
PORT=9999
NODE_ENV=development

# Blockchain Configuration
BLOCKCHAIN_NETWORK=development
CONTRACT_ADDRESS=
PRIVATE_KEY=

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

### 4. Start MongoDB

Ensure MongoDB is running on your system:

```bash
# On Windows (if MongoDB is installed as a service)
net start MongoDB

# On macOS (using Homebrew)
brew services start mongodb-community

# On Linux (using systemd)
sudo systemctl start mongod
```

### 5. Start Ganache (Blockchain)

In a new terminal window, start the local blockchain:

```bash
npx ganache-cli --deterministic --accounts 10 --host 127.0.0.1 --port 8545
```

Keep this terminal running throughout development.

### 6. Compile and Deploy Smart Contracts

```bash
# Compile contracts
npm run compile

# Deploy contracts to local blockchain
npm run migrate
```

### 7. Create Test Users

```bash
node scripts/createTestUsers.js
```

This will create test users with the following credentials:

- **Admin**: admin / admin123
- **Police Officer 1**: officer1 / police123
- **Lawyer**: lawyer1 / lawyer123
- **Police Officer 2**: officer2 / police123

### 8. Start the Backend Server

```bash
npm run dev
```

The server will start on the port specified in your `.env` file (default: 9999).

### 9. Access the Frontend

Open your web browser and navigate to:

```
http://localhost:9999
```

Or open the `frontend/index.html` file directly in your browser.

## System Architecture

### Components

1. **Frontend** (`frontend/`)
   - HTML5/CSS3/JavaScript web interface
   - Responsive design for desktop and mobile
   - Real-time updates and notifications

2. **Backend** (`backend/`)
   - Node.js/Express.js REST API
   - MongoDB database integration
   - JWT authentication
   - File upload handling
   - Blockchain interaction

3. **Smart Contracts** (`contracts/`)
   - Solidity smart contracts
   - Evidence storage and verification
   - Role-based access control
   - Immutable audit trail

4. **Database** (MongoDB)
   - User management
   - Evidence metadata
   - Access logs
   - Verification history

### Security Features

- **Authentication**: JWT-based user authentication
- **Authorization**: Role-based access control (Admin, Police, Lawyer)
- **File Integrity**: SHA-256 hashing for evidence files
- **Blockchain**: Immutable evidence storage on Ethereum
- **Encryption**: Secure password hashing with bcrypt
- **Rate Limiting**: Protection against brute force attacks

## User Roles

### Administrator
- Create and manage user accounts
- Grant blockchain roles
- System configuration
- Full access to all evidence

### Police Officer
- Upload evidence files
- View and verify evidence
- Download evidence files
- Access case-related evidence

### Lawyer
- View and verify evidence
- Download evidence files
- Access case-related evidence
- Cannot upload new evidence

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update profile
- `POST /api/auth/change-password` - Change password
- `POST /api/auth/register` - Register new user (Admin only)

### Evidence Management
- `POST /api/evidence/upload` - Upload evidence (Police only)
- `GET /api/evidence` - Get evidence list
- `GET /api/evidence/:id` - Get specific evidence
- `POST /api/evidence/:id/verify` - Verify evidence integrity
- `GET /api/evidence/:id/download` - Download evidence file
- `GET /api/evidence/case/:caseId` - Get evidence by case ID

### System
- `GET /api/health` - System health check
- `GET /api/blockchain/status` - Blockchain status

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Change the PORT in `.env` file
   - Kill existing processes: `pkill -f node`

2. **MongoDB Connection Error**
   - Ensure MongoDB is running
   - Check connection string in `.env`

3. **Blockchain Connection Error**
   - Ensure Ganache is running on port 8545
   - Redeploy contracts: `npm run migrate`

4. **File Upload Issues**
   - Check file size limits (50MB default)
   - Ensure uploads directory exists and is writable

### Logs

- Backend logs: Check console output
- Blockchain logs: Check Ganache terminal
- Frontend logs: Check browser developer console

## Development

### File Structure

```
EVIDENCE_PROT/
├── backend/           # Node.js backend
│   ├── models/        # Database models
│   ├── routes/        # API routes
│   ├── middleware/    # Express middleware
│   ├── utils/         # Utility functions
│   └── server.js      # Main server file
├── frontend/          # Web frontend
│   ├── css/           # Stylesheets
│   ├── js/            # JavaScript files
│   └── index.html     # Main HTML file
├── contracts/         # Solidity smart contracts
├── migrations/        # Truffle migration scripts
├── test/             # Test files
├── config/           # Configuration files
├── docs/             # Documentation
├── scripts/          # Utility scripts
└── uploads/          # Evidence file storage
```

### Adding New Features

1. **Backend**: Add routes in `backend/routes/`
2. **Frontend**: Add JavaScript modules in `frontend/js/`
3. **Smart Contracts**: Add contracts in `contracts/`
4. **Database**: Add models in `backend/models/`

### Testing

```bash
# Run tests
npm test

# Run specific test file
npm test -- test/evidence.test.js
```

## Production Deployment

### Environment Variables

Update `.env` for production:

```env
NODE_ENV=production
MONGODB_URI=mongodb://your-production-db
JWT_SECRET=your-production-secret
PORT=80
```

### Security Considerations

1. Use HTTPS in production
2. Configure proper CORS settings
3. Set up proper database authentication
4. Use environment-specific blockchain networks
5. Implement proper logging and monitoring
6. Regular security audits

### Docker Deployment

```bash
# Build Docker image
docker build -t evidence-protection .

# Run container
docker run -p 80:80 evidence-protection
```

## Support

For technical support or questions:

1. Check the troubleshooting section
2. Review system logs
3. Consult the API documentation
4. Contact the development team

## License

This project is licensed under the MIT License. See LICENSE file for details.
