const multer = require('multer');
const crypto = require('crypto');
const path = require('path');
const fs = require('fs').promises;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../../uploads');
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // Generate unique filename with timestamp and random string
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    const baseName = path.basename(file.originalname, extension);
    cb(null, `${baseName}-${uniqueSuffix}${extension}`);
  }
});

// File filter to restrict file types
const fileFilter = (req, file, cb) => {
  // Allowed file types for evidence
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/tiff',
    'video/mp4',
    'video/avi',
    'video/mov',
    'video/wmv',
    'audio/mp3',
    'audio/wav',
    'audio/aac',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/zip',
    'application/x-rar-compressed'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} is not allowed for evidence upload`), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 1 // Only one file at a time
  }
});

/**
 * Calculate SHA-256 hash of a file
 * @param {string} filePath - Path to the file
 * @returns {Promise<string>} - SHA-256 hash in hexadecimal
 */
async function calculateFileHash(filePath) {
  try {
    const fileBuffer = await fs.readFile(filePath);
    const hash = crypto.createHash('sha256');
    hash.update(fileBuffer);
    return hash.digest('hex');
  } catch (error) {
    throw new Error(`Error calculating file hash: ${error.message}`);
  }
}

/**
 * Verify file integrity by comparing hashes
 * @param {string} filePath - Path to the file
 * @param {string} expectedHash - Expected SHA-256 hash
 * @returns {Promise<boolean>} - True if hashes match
 */
async function verifyFileIntegrity(filePath, expectedHash) {
  try {
    const actualHash = await calculateFileHash(filePath);
    return actualHash === expectedHash;
  } catch (error) {
    console.error('File integrity verification error:', error);
    return false;
  }
}

/**
 * Get file metadata
 * @param {string} filePath - Path to the file
 * @returns {Promise<object>} - File metadata
 */
async function getFileMetadata(filePath) {
  try {
    const stats = await fs.stat(filePath);
    return {
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      accessed: stats.atime
    };
  } catch (error) {
    throw new Error(`Error getting file metadata: ${error.message}`);
  }
}

/**
 * Delete file safely
 * @param {string} filePath - Path to the file to delete
 * @returns {Promise<boolean>} - True if deleted successfully
 */
async function deleteFile(filePath) {
  try {
    await fs.unlink(filePath);
    return true;
  } catch (error) {
    console.error('File deletion error:', error);
    return false;
  }
}

/**
 * Check if file exists
 * @param {string} filePath - Path to check
 * @returns {Promise<boolean>} - True if file exists
 */
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Validate file for evidence upload
 * @param {object} file - Multer file object
 * @returns {object} - Validation result
 */
function validateEvidenceFile(file) {
  const errors = [];

  // Check file size (50MB limit)
  if (file.size > 50 * 1024 * 1024) {
    errors.push('File size exceeds 50MB limit');
  }

  // Check filename length
  if (file.originalname.length > 255) {
    errors.push('Filename is too long (max 255 characters)');
  }

  // Check for potentially dangerous file extensions
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
  const fileExtension = path.extname(file.originalname).toLowerCase();
  
  if (dangerousExtensions.includes(fileExtension)) {
    errors.push('File type is not allowed for security reasons');
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * Create upload directory if it doesn't exist
 */
async function ensureUploadDirectory() {
  try {
    const uploadPath = path.join(__dirname, '../../uploads');
    await fs.mkdir(uploadPath, { recursive: true });
    
    // Create .gitkeep file to ensure directory is tracked
    const gitkeepPath = path.join(uploadPath, '.gitkeep');
    const gitkeepExists = await fileExists(gitkeepPath);
    
    if (!gitkeepExists) {
      await fs.writeFile(gitkeepPath, '');
    }
    
    return true;
  } catch (error) {
    console.error('Error creating upload directory:', error);
    return false;
  }
}

module.exports = {
  upload,
  calculateFileHash,
  verifyFileIntegrity,
  getFileMetadata,
  deleteFile,
  fileExists,
  validateEvidenceFile,
  ensureUploadDirectory
};
