// Utility Functions

// Show/Hide loading overlay
function showLoading() {
    document.getElementById('loading-overlay').classList.add('show');
}

function hideLoading() {
    document.getElementById('loading-overlay').classList.remove('show');
}

// Enhanced Toast notifications
function showToast(message, type = 'info', duration = 5000, details = null) {
    const toastContainer = document.getElementById('toast-container');
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    const toastId = 'toast-' + Date.now();
    toast.id = toastId;

    toast.innerHTML = `
        <div class="toast-content">
            <div class="toast-icon">${getToastIcon(type)}</div>
            <div class="toast-text">
                <div class="toast-message">${message}</div>
                ${details ? `<div class="toast-details">${details}</div>` : ''}
            </div>
        </div>
        <button class="toast-close" onclick="dismissToast('${toastId}')">&times;</button>
    `;

    toastContainer.appendChild(toast);

    // Auto remove toast
    setTimeout(() => {
        dismissToast(toastId);
    }, duration);

    // Click to dismiss (except close button)
    toast.addEventListener('click', (e) => {
        if (!e.target.classList.contains('toast-close')) {
            dismissToast(toastId);
        }
    });

    return toastId;
}

// Dismiss specific toast
function dismissToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast && toast.parentNode) {
        toast.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

// Show success notification with details
function showSuccessNotification(title, details = null, duration = 6000) {
    return showToast(title, 'success', duration, details);
}

// Show error notification with details
function showErrorNotification(title, details = null, duration = 8000) {
    return showToast(title, 'error', duration, details);
}

// Show info notification with details
function showInfoNotification(title, details = null, duration = 5000) {
    return showToast(title, 'info', duration, details);
}

// Show warning notification with details
function showWarningNotification(title, details = null, duration = 6000) {
    return showToast(title, 'warning', duration, details);
}

function getToastIcon(type) {
    const icons = {
        success: '<i class="fas fa-check-circle"></i>',
        error: '<i class="fas fa-exclamation-circle"></i>',
        warning: '<i class="fas fa-exclamation-triangle"></i>',
        info: '<i class="fas fa-info-circle"></i>'
    };
    return icons[type] || icons.info;
}

// Modal functions
function showModal(title, content) {
    const modal = document.getElementById('modal');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');

    modalTitle.textContent = title;
    modalBody.innerHTML = content;
    modal.classList.remove('hidden');
    modal.classList.add('show');
}

function hideModal() {
    const modal = document.getElementById('modal');
    modal.classList.remove('show');
    modal.classList.add('hidden');
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Get file icon based on mime type
function getFileIcon(mimeType) {
    if (mimeType.startsWith('image/')) {
        return '<i class="fas fa-image"></i>';
    } else if (mimeType.startsWith('video/')) {
        return '<i class="fas fa-video"></i>';
    } else if (mimeType.startsWith('audio/')) {
        return '<i class="fas fa-music"></i>';
    } else if (mimeType === 'application/pdf') {
        return '<i class="fas fa-file-pdf"></i>';
    } else if (mimeType.includes('word') || mimeType.includes('document')) {
        return '<i class="fas fa-file-word"></i>';
    } else if (mimeType.includes('zip') || mimeType.includes('rar')) {
        return '<i class="fas fa-file-archive"></i>';
    } else {
        return '<i class="fas fa-file"></i>';
    }
}

// Validate form data
function validateForm(formElement) {
    const errors = [];
    const inputs = formElement.querySelectorAll('input[required], textarea[required], select[required]');

    inputs.forEach(input => {
        if (!input.value.trim()) {
            errors.push(`${input.name || input.id} is required`);
        }
    });

    // Email validation
    const emailInputs = formElement.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        if (input.value && !isValidEmail(input.value)) {
            errors.push('Please enter a valid email address');
        }
    });

    return errors;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Copy to clipboard
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast('Copied to clipboard', 'success');
    } catch (err) {
        console.error('Failed to copy: ', err);
        showToast('Failed to copy to clipboard', 'error');
    }
}

// Download file from blob
function downloadBlob(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// Generate random ID
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

// Escape HTML
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Truncate text
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substr(0, maxLength) + '...';
}

// Check if user is authenticated
function isAuthenticated() {
    return !!localStorage.getItem('token');
}

// Get user role from token
function getUserRole() {
    const token = localStorage.getItem('token');
    if (!token) return null;

    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.role;
    } catch (error) {
        console.error('Error parsing token:', error);
        return null;
    }
}

// Check if user has required role
function hasRole(requiredRole) {
    const userRole = getUserRole();
    if (!userRole) return false;

    const roleHierarchy = {
        admin: ['admin', 'police', 'lawyer'],
        police: ['police'],
        lawyer: ['lawyer']
    };

    return roleHierarchy[userRole]?.includes(requiredRole) || false;
}

// Format blockchain address
function formatAddress(address) {
    if (!address) return 'N/A';
    return `${address.substr(0, 6)}...${address.substr(-4)}`;
}

// Create pagination
function createPagination(currentPage, totalPages, onPageChange) {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    let html = '';

    // Previous button
    if (currentPage > 1) {
        html += `<button class="btn btn-secondary" onclick="${onPageChange}(${currentPage - 1})">
            <i class="fas fa-chevron-left"></i> Previous
        </button>`;
    }

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'btn-primary' : 'btn-secondary';
        html += `<button class="btn ${activeClass}" onclick="${onPageChange}(${i})">${i}</button>`;
    }

    // Next button
    if (currentPage < totalPages) {
        html += `<button class="btn btn-secondary" onclick="${onPageChange}(${currentPage + 1})">
            Next <i class="fas fa-chevron-right"></i>
        </button>`;
    }

    pagination.innerHTML = html;
}

// Initialize tooltips (if using a tooltip library)
function initializeTooltips() {
    // This would initialize tooltips if using a library like Tippy.js
    // For now, we'll use the browser's built-in title attribute
}

// Handle file preview
function previewFile(file, previewElement) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        let html = '';
        
        if (file.type.startsWith('image/')) {
            html = `<img src="${e.target.result}" alt="Preview" style="max-width: 100%; max-height: 200px;">`;
        } else if (file.type.startsWith('video/')) {
            html = `<video controls style="max-width: 100%; max-height: 200px;">
                <source src="${e.target.result}" type="${file.type}">
                Your browser does not support the video tag.
            </video>`;
        } else if (file.type.startsWith('audio/')) {
            html = `<audio controls>
                <source src="${e.target.result}" type="${file.type}">
                Your browser does not support the audio tag.
            </audio>`;
        } else {
            html = `<div class="file-info">
                ${getFileIcon(file.type)}
                <div>
                    <strong>${file.name}</strong><br>
                    <small>${formatFileSize(file.size)}</small>
                </div>
            </div>`;
        }
        
        previewElement.innerHTML = html;
    };
    
    if (file.type.startsWith('image/') || file.type.startsWith('video/') || file.type.startsWith('audio/')) {
        reader.readAsDataURL(file);
    } else {
        // For non-media files, just show file info
        previewElement.innerHTML = `<div class="file-info">
            ${getFileIcon(file.type)}
            <div>
                <strong>${file.name}</strong><br>
                <small>${formatFileSize(file.size)}</small>
            </div>
        </div>`;
    }
}
