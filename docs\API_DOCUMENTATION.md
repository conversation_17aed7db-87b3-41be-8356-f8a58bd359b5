# Evidence Protection System - API Documentation

## Base URL

```
http://localhost:9999/api
```

## Authentication

All protected endpoints require a JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow this format:

```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Response data (if applicable)
  "error": "Error message" // Only present if success is false
}
```

## Authentication Endpoints

### POST /auth/login

Login user and receive JWT token.

**Request Body:**
```json
{
  "username": "string", // Username or email
  "password": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "token": "jwt_token_here",
  "user": {
    "id": "user_id",
    "username": "username",
    "email": "email",
    "role": "police|lawyer|admin",
    "fullName": "Full Name",
    "walletAddress": "0x..."
  }
}
```

### GET /auth/me

Get current user information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_id",
    "username": "username",
    "email": "email",
    "role": "police|lawyer|admin",
    "firstName": "First",
    "lastName": "Last",
    "department": "Department",
    "walletAddress": "0x...",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "lastLogin": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /auth/profile

Update user profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "firstName": "string",
  "lastName": "string",
  "department": "string",
  "walletAddress": "string"
}
```

### POST /auth/change-password

Change user password.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "currentPassword": "string",
  "newPassword": "string"
}
```

### POST /auth/register

Register new user (Admin only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "role": "police|lawyer|admin",
  "firstName": "string",
  "lastName": "string",
  "badgeNumber": "string", // Required for police
  "barNumber": "string", // Required for lawyers
  "department": "string",
  "walletAddress": "string"
}
```

## Evidence Endpoints

### POST /evidence/upload

Upload new evidence (Police only).

**Headers:** 
- `Authorization: Bearer <token>`
- `Content-Type: multipart/form-data`

**Form Data:**
- `evidenceFile`: File (max 50MB)
- `caseId`: string
- `description`: string
- `tags`: string (comma-separated)

**Response:**
```json
{
  "success": true,
  "message": "Evidence uploaded successfully",
  "evidence": {
    "id": "evidence_id",
    "evidenceId": 12345,
    "fileName": "original_filename.jpg",
    "fileHash": "sha256_hash",
    "description": "Evidence description",
    "caseId": "CASE-001",
    "uploadedAt": "2024-01-01T00:00:00.000Z",
    "blockchainTxHash": "0x...",
    "isVerified": true
  }
}
```

### GET /evidence

Get evidence list with pagination and filtering.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page`: number (default: 1)
- `limit`: number (default: 10)
- `caseId`: string (filter by case ID)
- `uploader`: string (filter by uploader ID)
- `verified`: boolean (filter by verification status)

**Response:**
```json
{
  "success": true,
  "evidences": [
    {
      "id": "evidence_id",
      "evidenceId": 12345,
      "originalName": "filename.jpg",
      "fileHash": "sha256_hash",
      "fileSize": 1024000,
      "mimeType": "image/jpeg",
      "description": "Evidence description",
      "caseId": "CASE-001",
      "uploader": {
        "username": "officer1",
        "firstName": "John",
        "lastName": "Smith",
        "role": "police"
      },
      "isVerified": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5
  }
}
```

### GET /evidence/:id

Get specific evidence details.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "evidence": {
    "id": "evidence_id",
    "evidenceId": 12345,
    "originalName": "filename.jpg",
    "fileHash": "sha256_hash",
    "filePath": "/uploads/filename.jpg",
    "fileSize": 1024000,
    "mimeType": "image/jpeg",
    "description": "Evidence description",
    "caseId": "CASE-001",
    "uploader": {
      "username": "officer1",
      "firstName": "John",
      "lastName": "Smith",
      "role": "police"
    },
    "uploaderAddress": "0x...",
    "blockchainTxHash": "0x...",
    "blockNumber": 12345,
    "isVerified": true,
    "verificationAttempts": [
      {
        "verifier": {
          "username": "lawyer1",
          "firstName": "Sarah",
          "lastName": "Johnson"
        },
        "timestamp": "2024-01-01T00:00:00.000Z",
        "result": true
      }
    ],
    "accessLog": [
      {
        "accessor": {
          "username": "officer1",
          "firstName": "John",
          "lastName": "Smith"
        },
        "action": "view",
        "timestamp": "2024-01-01T00:00:00.000Z",
        "ipAddress": "***********"
      }
    ],
    "tags": ["weapon", "fingerprint"],
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### POST /evidence/:id/verify

Verify evidence integrity.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "verification": {
    "isValid": true,
    "fileIntegrityValid": true,
    "blockchainValid": true,
    "verifiedAt": "2024-01-01T00:00:00.000Z",
    "verifiedBy": {
      "id": "user_id",
      "username": "lawyer1",
      "fullName": "Sarah Johnson"
    }
  }
}
```

### GET /evidence/:id/download

Download evidence file.

**Headers:** `Authorization: Bearer <token>`

**Response:** File download with appropriate headers

### GET /evidence/case/:caseId

Get evidence by case ID.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "caseId": "CASE-001",
  "evidences": [
    // Array of evidence objects
  ],
  "count": 5
}
```

## System Endpoints

### GET /health

System health check (no authentication required).

**Response:**
```json
{
  "success": true,
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "database": "connected",
    "blockchain": "connected",
    "accounts": 10
  }
}
```

### GET /blockchain/status

Blockchain status (no authentication required).

**Response:**
```json
{
  "success": true,
  "blockchain": {
    "connected": true,
    "accounts": 10,
    "totalEvidences": 25,
    "network": "development"
  }
}
```

## Error Codes

### HTTP Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `429`: Too Many Requests
- `500`: Internal Server Error

### Common Error Responses

**401 Unauthorized:**
```json
{
  "success": false,
  "message": "Access token required"
}
```

**403 Forbidden:**
```json
{
  "success": false,
  "message": "Access denied. Required role: police"
}
```

**400 Bad Request:**
```json
{
  "success": false,
  "message": "Validation error",
  "errors": [
    "Case ID is required",
    "File size exceeds limit"
  ]
}
```

**429 Too Many Requests:**
```json
{
  "success": false,
  "message": "Too many requests from this IP, please try again later"
}
```

## Rate Limiting

- **Authentication endpoints**: 5 requests per 15 minutes per IP
- **General endpoints**: 100 requests per 15 minutes per IP
- **File upload**: 10 requests per hour per user

## File Upload Specifications

### Supported File Types

- **Images**: JPEG, PNG, GIF, BMP, TIFF
- **Videos**: MP4, AVI, MOV, WMV
- **Audio**: MP3, WAV, AAC
- **Documents**: PDF, TXT, DOC, DOCX
- **Archives**: ZIP, RAR

### File Size Limits

- Maximum file size: 50MB
- Maximum files per request: 1

### File Validation

- File type validation based on MIME type
- File size validation
- Malicious file detection
- File name sanitization

## Security Considerations

### Authentication

- JWT tokens expire after 7 days (configurable)
- Tokens must be included in Authorization header
- Failed login attempts are rate limited

### Authorization

- Role-based access control (RBAC)
- Police can upload evidence
- Lawyers can view and verify evidence
- Admins have full access

### Data Protection

- All file uploads are hashed with SHA-256
- Files are stored securely on the server
- Access is logged for audit purposes
- Blockchain provides immutable records

## SDK Examples

### JavaScript/Node.js

```javascript
const API_BASE = 'http://localhost:9999/api';

// Login
const login = async (username, password) => {
  const response = await fetch(`${API_BASE}/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  });
  return response.json();
};

// Upload evidence
const uploadEvidence = async (token, formData) => {
  const response = await fetch(`${API_BASE}/evidence/upload`, {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` },
    body: formData
  });
  return response.json();
};

// Get evidence
const getEvidence = async (token, evidenceId) => {
  const response = await fetch(`${API_BASE}/evidence/${evidenceId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};
```

### Python

```python
import requests

API_BASE = 'http://localhost:9999/api'

# Login
def login(username, password):
    response = requests.post(f'{API_BASE}/auth/login', json={
        'username': username,
        'password': password
    })
    return response.json()

# Upload evidence
def upload_evidence(token, file_path, case_id, description):
    with open(file_path, 'rb') as f:
        files = {'evidenceFile': f}
        data = {'caseId': case_id, 'description': description}
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.post(f'{API_BASE}/evidence/upload', 
                               files=files, data=data, headers=headers)
    return response.json()
```
