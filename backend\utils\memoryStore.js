const bcrypt = require('bcryptjs');

// In-memory user store for development/testing
class MemoryUserStore {
    constructor() {
        this.users = new Map();
        this.initializeDefaultUsers();
    }

    async initializeDefaultUsers() {
        // Create default admin user
        const hashedPassword = await bcrypt.hash('admin123', 12);
        
        const adminUser = {
            id: 'admin-001',
            username: 'admin',
            email: '<EMAIL>',
            password: hashedPassword,
            role: 'admin',
            firstName: 'System',
            lastName: 'Administrator',
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.users.set('admin', adminUser);
        this.users.set('<EMAIL>', adminUser);
        
        console.log('✅ Memory store initialized with default admin user');
        console.log('Login credentials: admin / admin123');
    }

    async findUser(identifier) {
        // Find by username or email
        return this.users.get(identifier) || null;
    }

    async findUserById(id) {
        for (const user of this.users.values()) {
            if (user.id === id) {
                return user;
            }
        }
        return null;
    }

    async createUser(userData) {
        const userId = `user-${Date.now()}`;
        const hashedPassword = await bcrypt.hash(userData.password, 12);
        
        const user = {
            id: userId,
            username: userData.username,
            email: userData.email,
            password: hashedPassword,
            role: userData.role,
            firstName: userData.firstName,
            lastName: userData.lastName,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.users.set(userData.username, user);
        this.users.set(userData.email, user);
        
        return user;
    }

    async comparePassword(user, candidatePassword) {
        return await bcrypt.compare(candidatePassword, user.password);
    }

    async updateUser(userId, updateData) {
        const user = await this.findUserById(userId);
        if (!user) return null;

        Object.assign(user, updateData, { updatedAt: new Date() });
        return user;
    }

    async deleteUser(userId) {
        const user = await this.findUserById(userId);
        if (!user) return false;

        this.users.delete(user.username);
        this.users.delete(user.email);
        return true;
    }

    async getAllUsers() {
        const uniqueUsers = new Map();
        for (const user of this.users.values()) {
            uniqueUsers.set(user.id, user);
        }
        return Array.from(uniqueUsers.values());
    }
}

module.exports = new MemoryUserStore();
