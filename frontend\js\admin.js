// Admin Panel Management
class AdminManager {
    constructor() {
        this.currentPage = 1;
        this.currentSearch = '';
        this.currentRole = '';
        this.editingUserId = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // User search
        const searchInput = document.getElementById('user-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentSearch = e.target.value;
                this.loadUsers();
            });
        }

        // Role filter
        const roleFilter = document.getElementById('role-filter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.currentRole = e.target.value;
                this.loadUsers();
            });
        }

        // Add user button
        const addUserBtn = document.getElementById('add-user-btn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => this.showAddUserModal());
        }

        // User form submission
        const userForm = document.getElementById('user-form');
        if (userForm) {
            userForm.addEventListener('submit', (e) => this.handleUserSubmit(e));
        }

        // Blockchain health refresh
        const refreshBlockchainBtn = document.getElementById('refresh-blockchain-health');
        if (refreshBlockchainBtn) {
            refreshBlockchainBtn.addEventListener('click', () => this.loadBlockchainHealth());
        }

        // Logs refresh
        const refreshLogsBtn = document.getElementById('refresh-logs');
        if (refreshLogsBtn) {
            refreshLogsBtn.addEventListener('click', () => this.loadRealTimeLogs());
        }

        // Auto refresh toggle
        const autoRefreshBtn = document.getElementById('auto-refresh-toggle');
        if (autoRefreshBtn) {
            autoRefreshBtn.addEventListener('click', () => this.toggleAutoRefresh());
        }

        // Export case button
        const exportCaseBtn = document.getElementById('export-case-btn');
        if (exportCaseBtn) {
            exportCaseBtn.addEventListener('click', () => this.exportCase());
        }

        // Create sample evidence button
        const createSampleBtn = document.getElementById('create-sample-evidence-btn');
        if (createSampleBtn) {
            createSampleBtn.addEventListener('click', () => this.createSampleEvidence());
        }
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        // Load content based on tab
        switch (tabName) {
            case 'users':
                this.loadUsers();
                break;
            case 'stats':
                this.loadStats();
                break;
            case 'blockchain':
                this.loadBlockchainHealth();
                break;
            case 'logs':
                this.loadRealTimeLogs();
                break;
            case 'export':
                // Export tab doesn't need initial loading
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    async loadUsers() {
        try {
            showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: 10,
                search: this.currentSearch,
                role: this.currentRole
            });

            const response = await api.request(`/admin/users?${params}`, 'GET');
            
            if (response.success) {
                this.displayUsers(response.users);
                this.displayPagination(response.pagination);
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Load users error:', error);
            showErrorNotification('Error', 'Failed to load users');
        } finally {
            hideLoading();
        }
    }

    displayUsers(users) {
        const tbody = document.getElementById('users-table-body');
        if (!tbody) return;

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div class="user-name">${user.fullName}</div>
                            <div class="user-username">@${user.username}</div>
                            <div class="user-email">${user.email}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="role-badge role-${user.role}">${user.role.toUpperCase()}</span>
                </td>
                <td>${user.department || 'N/A'}</td>
                <td>
                    <span class="status-badge ${user.isActive ? 'active' : 'inactive'}">
                        ${user.isActive ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" onclick="adminManager.editUser('${user._id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="adminManager.resetPassword('${user._id}')" title="Reset Password">
                            <i class="fas fa-key"></i>
                        </button>
                        <button class="btn-icon ${user.isActive ? 'danger' : 'success'}" 
                                onclick="adminManager.toggleUserStatus('${user._id}', ${!user.isActive})" 
                                title="${user.isActive ? 'Deactivate' : 'Activate'}">
                            <i class="fas fa-${user.isActive ? 'ban' : 'check'}"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    displayPagination(pagination) {
        const container = document.getElementById('users-pagination');
        if (!container) return;

        const { current, pages, total } = pagination;
        
        let paginationHTML = `<div class="pagination-info">Showing ${total} users</div>`;
        
        if (pages > 1) {
            paginationHTML += '<div class="pagination-buttons">';
            
            // Previous button
            if (current > 1) {
                paginationHTML += `<button onclick="adminManager.changePage(${current - 1})">Previous</button>`;
            }
            
            // Page numbers
            for (let i = Math.max(1, current - 2); i <= Math.min(pages, current + 2); i++) {
                paginationHTML += `<button class="${i === current ? 'active' : ''}" onclick="adminManager.changePage(${i})">${i}</button>`;
            }
            
            // Next button
            if (current < pages) {
                paginationHTML += `<button onclick="adminManager.changePage(${current + 1})">Next</button>`;
            }
            
            paginationHTML += '</div>';
        }
        
        container.innerHTML = paginationHTML;
    }

    changePage(page) {
        this.currentPage = page;
        this.loadUsers();
    }

    showAddUserModal() {
        this.editingUserId = null;
        document.getElementById('user-modal-title').textContent = 'Add New User';
        document.getElementById('user-submit-text').textContent = 'Create User';
        document.getElementById('password-group').style.display = 'block';
        document.getElementById('user-form').reset();
        document.getElementById('user-modal').classList.remove('hidden');
    }

    async editUser(userId) {
        try {
            // Get user data
            const response = await api.request(`/admin/users`, 'GET');
            const user = response.users.find(u => u._id === userId);
            
            if (!user) {
                showErrorNotification('Error', 'User not found');
                return;
            }

            this.editingUserId = userId;
            document.getElementById('user-modal-title').textContent = 'Edit User';
            document.getElementById('user-submit-text').textContent = 'Update User';
            document.getElementById('password-group').style.display = 'none';
            
            // Fill form with user data
            document.getElementById('user-username').value = user.username;
            document.getElementById('user-email').value = user.email;
            document.getElementById('user-fullname').value = user.fullName;
            document.getElementById('user-role').value = user.role;
            document.getElementById('user-department').value = user.department || '';
            
            document.getElementById('user-modal').classList.remove('hidden');
        } catch (error) {
            console.error('Edit user error:', error);
            showErrorNotification('Error', 'Failed to load user data');
        }
    }

    async handleUserSubmit(event) {
        event.preventDefault();
        
        const formData = {
            username: document.getElementById('user-username').value,
            email: document.getElementById('user-email').value,
            fullName: document.getElementById('user-fullname').value,
            role: document.getElementById('user-role').value,
            department: document.getElementById('user-department').value
        };

        if (!this.editingUserId) {
            formData.password = document.getElementById('user-password').value;
        }

        try {
            showLoading();
            
            let response;
            if (this.editingUserId) {
                response = await api.request(`/admin/users/${this.editingUserId}`, 'PUT', formData);
            } else {
                response = await api.request('/admin/users', 'POST', formData);
            }
            
            if (response.success) {
                showSuccessNotification(
                    'Success', 
                    this.editingUserId ? 'User updated successfully' : 'User created successfully'
                );
                this.closeUserModal();
                this.loadUsers();
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('User submit error:', error);
            showErrorNotification('Error', 'Failed to save user');
        } finally {
            hideLoading();
        }
    }

    closeUserModal() {
        document.getElementById('user-modal').classList.add('hidden');
        this.editingUserId = null;
    }

    async toggleUserStatus(userId, activate) {
        try {
            const response = await api.request(`/admin/users/${userId}`, 'PUT', {
                isActive: activate
            });
            
            if (response.success) {
                showSuccessNotification('Success', `User ${activate ? 'activated' : 'deactivated'} successfully`);
                this.loadUsers();
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Toggle user status error:', error);
            showErrorNotification('Error', 'Failed to update user status');
        }
    }

    async resetPassword(userId) {
        const newPassword = prompt('Enter new password (minimum 6 characters):');
        if (!newPassword || newPassword.length < 6) {
            showErrorNotification('Error', 'Password must be at least 6 characters long');
            return;
        }

        try {
            const response = await api.request(`/admin/users/${userId}/reset-password`, 'POST', {
                newPassword
            });
            
            if (response.success) {
                showSuccessNotification('Success', 'Password reset successfully');
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Reset password error:', error);
            showErrorNotification('Error', 'Failed to reset password');
        }
    }

    async loadStats() {
        try {
            const response = await api.request('/admin/stats', 'GET');
            
            if (response.success) {
                this.displayStats(response.stats);
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Load stats error:', error);
            showErrorNotification('Error', 'Failed to load statistics');
        }
    }

    displayStats(stats) {
        try {
            // Update stat cards with fallbacks
            const totalUsersEl = document.getElementById('total-users');
            const totalEvidenceEl = document.getElementById('total-evidence');
            const verifiedEvidenceEl = document.getElementById('verified-evidence');
            const verificationRateEl = document.getElementById('verification-rate');

            if (totalUsersEl) totalUsersEl.textContent = stats.users?.total || 0;
            if (totalEvidenceEl) totalEvidenceEl.textContent = stats.evidence?.total || 0;
            if (verifiedEvidenceEl) verifiedEvidenceEl.textContent = stats.evidence?.verified || 0;
            if (verificationRateEl) verificationRateEl.textContent = (stats.evidence?.verificationRate || 0) + '%';

            // Display role distribution
            const roleChart = document.getElementById('role-chart');
            if (roleChart && stats.users?.byRole) {
                roleChart.innerHTML = stats.users.byRole.map(role => `
                    <div class="role-stat">
                        <span class="role-badge role-${role._id.toLowerCase()}">${role._id.toUpperCase()}</span>
                        <span class="role-count">${role.count}</span>
                    </div>
                `).join('');
            } else if (roleChart) {
                roleChart.innerHTML = '<p>No role data available</p>';
            }
        } catch (error) {
            console.error('Error displaying stats:', error);
        }

        // Display recent activity
        const activityList = document.getElementById('activity-list');
        if (activityList) {
            try {
                const activities = [];

                // Add user activities
                if (stats.users?.recent) {
                    activities.push(...stats.users.recent.map(user => ({
                        type: 'user',
                        text: `New user: ${user.firstName || 'Unknown'} ${user.lastName || ''}`.trim(),
                        date: user.createdAt
                    })));
                }

                // Add evidence activities
                if (stats.evidence?.recent) {
                    activities.push(...stats.evidence.recent.map(evidence => ({
                        type: 'evidence',
                        text: `Evidence uploaded: ${evidence.originalName || 'Unknown file'}`,
                        date: evidence.uploadedAt
                    })));
                }

                if (activities.length > 0) {
                    const sortedActivities = activities
                        .sort((a, b) => new Date(b.date) - new Date(a.date))
                        .slice(0, 10);

                    activityList.innerHTML = sortedActivities.map(activity => `
                        <div class="activity-item">
                            <i class="fas fa-${activity.type === 'user' ? 'user-plus' : 'upload'}"></i>
                            <span>${activity.text}</span>
                            <small>${formatDate(activity.date)}</small>
                        </div>
                    `).join('');
                } else {
                    activityList.innerHTML = '<p>No recent activity</p>';
                }
            } catch (error) {
                console.error('Error displaying activity:', error);
                activityList.innerHTML = '<p>Error loading activity data</p>';
            }
        }
    }

    loadSettings() {
        // Load system settings
        console.log('Loading system settings...');
    }

    async loadBlockchainHealth() {
        const statusContainer = document.getElementById('blockchain-status');
        if (statusContainer) {
            statusContainer.innerHTML = '<div class="health-card"><h4>Loading blockchain health...</h4><i class="fas fa-spinner fa-spin"></i></div>';
        }

        try {
            const response = await api.getBlockchainHealth();
            if (response.success) {
                this.displayBlockchainHealth(response.health);
            } else {
                this.displayBlockchainError('Failed to load blockchain health data');
            }
        } catch (error) {
            console.error('Error loading blockchain health:', error);
            this.displayBlockchainError('Error connecting to blockchain service');
        }
    }

    displayBlockchainHealth(health) {
        const statusContainer = document.getElementById('blockchain-status');
        if (!statusContainer) return;

        const statusClass = health.status === 'Connected' ? 'connected' :
                           health.status === 'Disconnected' ? 'disconnected' : 'error';

        statusContainer.innerHTML = `
            <div class="health-card ${statusClass}">
                <h4>
                    <span class="status-indicator ${statusClass}"></span>
                    Blockchain Status
                </h4>
                <div class="health-metric">
                    <span class="metric-label">Contract Status:</span>
                    <span class="metric-value">${health.contractStatus}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">⛽ Avg Gas Used:</span>
                    <span class="metric-value">${health.avgGasUsed.toLocaleString()}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">🧾 Events Emitted:</span>
                    <span class="metric-value">${health.eventsEmitted}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">🔐 Roles Assigned:</span>
                    <span class="metric-value">${health.rolesAssigned.uploader} Uploader, ${health.rolesAssigned.admin} Admin</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">📈 Network:</span>
                    <span class="metric-value">${health.network}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">Accounts:</span>
                    <span class="metric-value">${health.accounts || 0}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">Last Updated:</span>
                    <span class="metric-value">${formatDate(health.lastUpdated)}</span>
                </div>
            </div>
        `;
    }

    displayBlockchainError(message) {
        const statusContainer = document.getElementById('blockchain-status');
        if (!statusContainer) return;

        statusContainer.innerHTML = `
            <div class="health-card error">
                <h4>
                    <span class="status-indicator error"></span>
                    Blockchain Error
                </h4>
                <p style="color: #856404; margin-top: 1rem;">${message}</p>
            </div>
        `;
    }

    async loadRealTimeLogs() {
        try {
            const response = await api.getRealTimeLogs();
            if (response.success) {
                this.displayRealTimeLogs(response.logs);
            } else {
                this.displayLogsError('Failed to load real-time logs');
            }
        } catch (error) {
            console.error('Error loading real-time logs:', error);
            this.displayLogsError('Error connecting to logging service');
        }
    }

    displayRealTimeLogs(logs) {
        const accessLogsContainer = document.getElementById('access-logs');
        const verificationLogsContainer = document.getElementById('verification-logs');

        if (accessLogsContainer) {
            if (logs.accessLogs && logs.accessLogs.length > 0) {
                accessLogsContainer.innerHTML = logs.accessLogs.map(log => `
                    <div class="log-entry">
                        <div>
                            <span class="log-timestamp">${formatDate(log.timestamp)}</span>
                            <span class="log-user">${log.user || 'Unknown'}</span>
                            <span class="log-action">${log.action.toUpperCase()}</span>
                            <span class="log-evidence">Evidence: ${log.evidenceId}</span>
                        </div>
                        <div class="log-ip">${log.ipAddress || 'N/A'}</div>
                    </div>
                `).join('');
            } else {
                accessLogsContainer.innerHTML = '<div class="log-entry">No recent access logs</div>';
            }
        }

        if (verificationLogsContainer) {
            if (logs.verificationLogs && logs.verificationLogs.length > 0) {
                verificationLogsContainer.innerHTML = logs.verificationLogs.map(log => `
                    <div class="log-entry">
                        <div>
                            <span class="log-timestamp">${formatDate(log.timestamp)}</span>
                            <span class="log-user">${log.user || 'Unknown'}</span>
                            <span class="log-action">VERIFY</span>
                            <span class="log-evidence">Evidence: ${log.evidenceId}</span>
                        </div>
                        <div class="log-result ${log.result ? 'success' : 'error'}">
                            ${log.result ? 'PASSED' : 'FAILED'}
                        </div>
                    </div>
                `).join('');
            } else {
                verificationLogsContainer.innerHTML = '<div class="log-entry">No recent verification logs</div>';
            }
        }
    }

    displayLogsError(message) {
        const accessLogsContainer = document.getElementById('access-logs');
        const verificationLogsContainer = document.getElementById('verification-logs');

        const errorHTML = `<div class="log-entry error">${message}</div>`;

        if (accessLogsContainer) accessLogsContainer.innerHTML = errorHTML;
        if (verificationLogsContainer) verificationLogsContainer.innerHTML = errorHTML;
    }

    toggleAutoRefresh() {
        const button = document.getElementById('auto-refresh-toggle');
        if (!button) return;

        if (this.autoRefreshInterval) {
            // Stop auto refresh
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
            button.classList.remove('active');
            button.innerHTML = '<i class="fas fa-play"></i> Auto Refresh';
        } else {
            // Start auto refresh
            this.autoRefreshInterval = setInterval(() => {
                this.loadRealTimeLogs();
            }, 5000); // Refresh every 5 seconds
            button.classList.add('active');
            button.innerHTML = '<i class="fas fa-pause"></i> Stop Auto Refresh';
        }
    }

    async exportCase() {
        const caseIdInput = document.getElementById('export-case-id');
        const caseId = caseIdInput?.value?.trim();

        if (!caseId) {
            showToast('Please enter a case ID', 'error');
            return;
        }

        try {
            showLoading();

            const blob = await api.exportLegalCase(caseId);

            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `case-${caseId}-legal-export.zip`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showToast('Legal export generated successfully!', 'success');

            // Clear the input
            if (caseIdInput) caseIdInput.value = '';

        } catch (error) {
            console.error('Export error:', error);
            showToast('Error generating legal export. Please check the case ID and try again.', 'error');
        } finally {
            hideLoading();
        }
    }

    async createSampleEvidence() {
        try {
            showLoading();

            const response = await api.createSampleEvidence();

            if (response.success) {
                showToast('Sample evidence created successfully!', 'success');
                console.log('Created evidence:', response.evidence);
            } else {
                showToast('Failed to create sample evidence', 'error');
            }
        } catch (error) {
            console.error('Create sample evidence error:', error);
            showToast('Error creating sample evidence', 'error');
        } finally {
            hideLoading();
        }
    }
}

// Global functions for modal
function closeUserModal() {
    if (window.adminManager) {
        window.adminManager.closeUserModal();
    }
}

// Create global admin manager instance
const adminManager = new AdminManager();
window.adminManager = adminManager;
