// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

/**
 * @title EvidenceProtection
 * @dev Smart contract for managing digital evidence with blockchain immutability
 */
contract EvidenceProtection is AccessControl, ReentrancyGuard {
    using Counters for Counters.Counter;
    
    // Role definitions
    bytes32 public constant POLICE_ROLE = keccak256("POLICE_ROLE");
    bytes32 public constant LAWYER_ROLE = keccak256("LAWYER_ROLE");
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    
    // Evidence counter
    Counters.Counter private _evidenceIds;
    
    // Evidence structure
    struct Evidence {
        uint256 id;
        string fileHash;        // SHA-256 hash of the file
        string fileName;        // Original file name
        string description;     // Evidence description
        address uploader;       // Address of the uploader
        uint256 timestamp;      // Upload timestamp
        string caseId;          // Associated case ID
        bool isActive;          // Evidence status
        string[] accessLog;     // Access history
    }
    
    // Mappings
    mapping(uint256 => Evidence) public evidences;
    mapping(string => uint256) public hashToEvidenceId;
    mapping(string => uint256[]) public caseToEvidenceIds;
    mapping(address => uint256[]) public uploaderToEvidenceIds;
    
    // Events
    event EvidenceUploaded(
        uint256 indexed evidenceId,
        string indexed fileHash,
        address indexed uploader,
        string caseId,
        uint256 timestamp
    );
    
    event EvidenceAccessed(
        uint256 indexed evidenceId,
        address indexed accessor,
        uint256 timestamp
    );
    
    event EvidenceDeactivated(
        uint256 indexed evidenceId,
        address indexed deactivator,
        uint256 timestamp
    );
    
    /**
     * @dev Constructor sets up roles
     */
    constructor() {
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(ADMIN_ROLE, msg.sender);
    }
    
    /**
     * @dev Upload new evidence to the blockchain
     * @param _fileHash SHA-256 hash of the evidence file
     * @param _fileName Original name of the file
     * @param _description Description of the evidence
     * @param _caseId Associated case identifier
     */
    function uploadEvidence(
        string memory _fileHash,
        string memory _fileName,
        string memory _description,
        string memory _caseId
    ) public onlyRole(POLICE_ROLE) nonReentrant returns (uint256) {
        require(bytes(_fileHash).length > 0, "File hash cannot be empty");
        require(bytes(_fileName).length > 0, "File name cannot be empty");
        require(bytes(_caseId).length > 0, "Case ID cannot be empty");
        require(hashToEvidenceId[_fileHash] == 0, "Evidence with this hash already exists");
        
        _evidenceIds.increment();
        uint256 newEvidenceId = _evidenceIds.current();
        
        Evidence storage newEvidence = evidences[newEvidenceId];
        newEvidence.id = newEvidenceId;
        newEvidence.fileHash = _fileHash;
        newEvidence.fileName = _fileName;
        newEvidence.description = _description;
        newEvidence.uploader = msg.sender;
        newEvidence.timestamp = block.timestamp;
        newEvidence.caseId = _caseId;
        newEvidence.isActive = true;
        
        // Update mappings
        hashToEvidenceId[_fileHash] = newEvidenceId;
        caseToEvidenceIds[_caseId].push(newEvidenceId);
        uploaderToEvidenceIds[msg.sender].push(newEvidenceId);
        
        emit EvidenceUploaded(newEvidenceId, _fileHash, msg.sender, _caseId, block.timestamp);
        
        return newEvidenceId;
    }
    
    /**
     * @dev Verify evidence integrity by checking hash
     * @param _evidenceId ID of the evidence to verify
     * @param _fileHash Hash to verify against
     */
    function verifyEvidence(uint256 _evidenceId, string memory _fileHash) 
        public 
        view 
        returns (bool) 
    {
        require(_evidenceId > 0 && _evidenceId <= _evidenceIds.current(), "Invalid evidence ID");
        require(evidences[_evidenceId].isActive, "Evidence is not active");
        
        return keccak256(abi.encodePacked(evidences[_evidenceId].fileHash)) == 
               keccak256(abi.encodePacked(_fileHash));
    }
    
    /**
     * @dev Get evidence details (restricted access)
     * @param _evidenceId ID of the evidence
     */
    function getEvidence(uint256 _evidenceId) 
        public 
        returns (Evidence memory) 
    {
        require(
            hasRole(POLICE_ROLE, msg.sender) || 
            hasRole(LAWYER_ROLE, msg.sender) || 
            hasRole(ADMIN_ROLE, msg.sender),
            "Unauthorized access"
        );
        require(_evidenceId > 0 && _evidenceId <= _evidenceIds.current(), "Invalid evidence ID");
        require(evidences[_evidenceId].isActive, "Evidence is not active");
        
        // Log access
        evidences[_evidenceId].accessLog.push(
            string(abi.encodePacked(
                "Accessed by: ",
                addressToString(msg.sender),
                " at: ",
                uint2str(block.timestamp)
            ))
        );
        
        emit EvidenceAccessed(_evidenceId, msg.sender, block.timestamp);
        
        return evidences[_evidenceId];
    }
    
    /**
     * @dev Get evidence by case ID
     * @param _caseId Case identifier
     */
    function getEvidenceByCase(string memory _caseId) 
        public 
        view 
        returns (uint256[] memory) 
    {
        require(
            hasRole(POLICE_ROLE, msg.sender) || 
            hasRole(LAWYER_ROLE, msg.sender) || 
            hasRole(ADMIN_ROLE, msg.sender),
            "Unauthorized access"
        );
        
        return caseToEvidenceIds[_caseId];
    }
    
    /**
     * @dev Deactivate evidence (admin only)
     * @param _evidenceId ID of evidence to deactivate
     */
    function deactivateEvidence(uint256 _evidenceId) 
        public 
        onlyRole(ADMIN_ROLE) 
    {
        require(_evidenceId > 0 && _evidenceId <= _evidenceIds.current(), "Invalid evidence ID");
        require(evidences[_evidenceId].isActive, "Evidence already inactive");
        
        evidences[_evidenceId].isActive = false;
        
        emit EvidenceDeactivated(_evidenceId, msg.sender, block.timestamp);
    }
    
    /**
     * @dev Grant police role to an address
     * @param _account Address to grant role to
     */
    function grantPoliceRole(address _account) public onlyRole(ADMIN_ROLE) {
        grantRole(POLICE_ROLE, _account);
    }
    
    /**
     * @dev Grant lawyer role to an address
     * @param _account Address to grant role to
     */
    function grantLawyerRole(address _account) public onlyRole(ADMIN_ROLE) {
        grantRole(LAWYER_ROLE, _account);
    }
    
    /**
     * @dev Get total number of evidences
     */
    function getTotalEvidences() public view returns (uint256) {
        return _evidenceIds.current();
    }
    
    // Utility functions
    function addressToString(address _addr) internal pure returns (string memory) {
        bytes32 value = bytes32(uint256(uint160(_addr)));
        bytes memory alphabet = "0123456789abcdef";
        bytes memory str = new bytes(42);
        str[0] = '0';
        str[1] = 'x';
        for (uint256 i = 0; i < 20; i++) {
            str[2+i*2] = alphabet[uint8(value[i + 12] >> 4)];
            str[3+i*2] = alphabet[uint8(value[i + 12] & 0x0f)];
        }
        return string(str);
    }
    
    function uint2str(uint256 _i) internal pure returns (string memory) {
        if (_i == 0) {
            return "0";
        }
        uint256 j = _i;
        uint256 len;
        while (j != 0) {
            len++;
            j /= 10;
        }
        bytes memory bstr = new bytes(len);
        uint256 k = len;
        while (_i != 0) {
            k = k-1;
            uint8 temp = (48 + uint8(_i - _i / 10 * 10));
            bytes1 b1 = bytes1(temp);
            bstr[k] = b1;
            _i /= 10;
        }
        return string(bstr);
    }
}
