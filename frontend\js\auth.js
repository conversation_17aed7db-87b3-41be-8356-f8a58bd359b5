// Authentication Management
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        // Wait for DOM to be ready before setting up forms
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupAfterDOM());
        } else {
            this.setupAfterDOM();
        }
    }

    setupAfterDOM() {
        // Check if user is already logged in
        if (isAuthenticated()) {
            this.loadCurrentUser();
        }

        // Setup login form
        this.setupLoginForm();
    }

    setupLoginForm() {
        console.log('🔧 Setting up login form...');
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            console.log('✅ Login form found, adding event listener');
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        } else {
            console.error('❌ Login form not found!');
        }
    }

    // Backup login function that can be called directly
    async performLogin() {
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        if (!usernameInput || !passwordInput) {
            return;
        }

        const credentials = {
            username: usernameInput.value.trim(),
            password: passwordInput.value.trim()
        };

        if (!credentials.username || !credentials.password) {
            showErrorNotification('Validation Error', 'Please enter both username and password');
            return;
        }

        try {
            showLoading();

            const response = await api.login(credentials);

            if (response.success) {
                // Store token and user data
                api.setToken(response.token);
                this.currentUser = response.user;

                showSuccessNotification(
                    'Login Successful!',
                    `Welcome back, ${response.user.fullName || response.user.username}. Role: ${response.user.role.toUpperCase()}`
                );

                // Show dashboard and update navigation
                this.showDashboard();

                // Prevent app from interfering with navigation
                if (window.app) {
                    window.app.currentPage = 'dashboard';
                    window.app.preventRedirect = true;
                }
            } else {
                showErrorNotification('Login Failed', response.message || 'Invalid credentials. Please try again.');
            }
        } catch (error) {
            console.error('Login error:', error);
            showErrorNotification('Login Error', 'Unable to connect to server. Please try again.');
        } finally {
            hideLoading();
        }
    }

    async handleLogin(event) {
        console.log('🔐 Login form submitted');
        event.preventDefault();

        const formData = new FormData(event.target);
        const credentials = {
            username: formData.get('username'),
            password: formData.get('password')
        };

        console.log('📝 Login credentials:', { username: credentials.username, password: credentials.password ? '[HIDDEN]' : '[EMPTY]' });

        // Validate form
        if (!credentials.username || !credentials.password) {
            showToast('Please enter both username and password', 'error');
            return;
        }

        try {
            showLoading();
            
            const response = await api.login(credentials);
            
            if (response.success) {
                // Store token and user data
                api.setToken(response.token);
                this.currentUser = response.user;

                showSuccessNotification(
                    'Login Successful!',
                    `Welcome back, ${response.user.fullName || response.user.username}. Role: ${response.user.role.toUpperCase()}`
                );

                // Show dashboard and update navigation
                this.showDashboard();

                // Prevent app from interfering with navigation
                if (window.app) {
                    window.app.currentPage = 'dashboard';
                    // Prevent app from redirecting back to home
                    window.app.preventRedirect = true;
                }
            } else {
                showErrorNotification('Login Failed', response.message || 'Invalid credentials. Please try again.');
            }
        } catch (error) {
            console.error('Login error:', error);
            showErrorNotification('Connection Error', 'Unable to connect to server. Please check your internet connection and try again.');
        } finally {
            hideLoading();
        }
    }



    async loadCurrentUser() {
        try {
            const response = await api.getCurrentUser();
            
            if (response.success) {
                this.currentUser = response.user;
                this.updateUserInterface();
                this.showDashboard();
            } else {
                this.logout();
            }
        } catch (error) {
            console.error('Load user error:', error);
            this.logout();
        }
    }

    updateUserInterface() {
        if (!this.currentUser) return;

        // Update navigation
        const navbar = document.getElementById('navbar');
        const userInfo = document.getElementById('user-info');
        const userName = document.getElementById('user-name');
        const userRole = document.getElementById('user-role');

        if (navbar) navbar.style.display = 'block';
        if (userName) userName.textContent = this.currentUser.fullName || this.currentUser.username;
        if (userRole) {
            userRole.textContent = this.currentUser.role.toUpperCase();
            userRole.className = `role-badge role-${this.currentUser.role}`;
        }

        // Show/hide navigation items based on role
        this.updateNavigationByRole();
    }

    updateNavigationByRole() {
        const uploadNav = document.getElementById('nav-upload');
        const adminNav = document.getElementById('nav-admin');

        // Only police can upload evidence
        if (uploadNav) {
            uploadNav.style.display = this.currentUser.role === 'police' ? 'flex' : 'none';
        }

        // Only admin can access admin panel
        if (adminNav) {
            adminNav.style.display = this.currentUser.role === 'admin' ? 'flex' : 'none';
        }
    }

    showLoginPage() {
        this.hideAllPages();
        document.getElementById('login-page').classList.remove('hidden');

        // Show navbar but hide authenticated items
        const navbar = document.getElementById('navbar');
        navbar.style.display = 'block';

        // Show public navigation items
        document.getElementById('nav-home').style.display = 'flex';
        document.getElementById('nav-about').style.display = 'flex';
        document.getElementById('nav-login').style.display = 'flex';

        // Hide authenticated navigation items
        document.getElementById('nav-dashboard').style.display = 'none';
        document.getElementById('nav-upload').style.display = 'none';
        document.getElementById('nav-evidence').style.display = 'none';
        document.getElementById('nav-verify').style.display = 'none';
        document.getElementById('nav-admin').style.display = 'none';
        document.getElementById('nav-profile').style.display = 'none';
        document.getElementById('nav-logout').style.display = 'none';

        // Hide user info
        document.getElementById('user-info').style.display = 'none';


    }

    showDashboard() {
        console.log('AuthManager: Showing dashboard...');

        this.hideAllPages();

        // Show dashboard page
        const dashboardPage = document.getElementById('dashboard-page');
        if (dashboardPage) {
            dashboardPage.classList.remove('hidden');
            console.log('AuthManager: Dashboard page shown');
        } else {
            console.error('AuthManager: Dashboard page not found');
        }

        document.getElementById('navbar').style.display = 'block';

        // Hide public navigation items
        document.getElementById('nav-home').style.display = 'none';
        document.getElementById('nav-about').style.display = 'none';
        document.getElementById('nav-login').style.display = 'none';

        // Show authenticated navigation items
        document.getElementById('nav-dashboard').style.display = 'flex';
        document.getElementById('nav-upload').style.display = 'flex';
        document.getElementById('nav-evidence').style.display = 'flex';
        document.getElementById('nav-verify').style.display = 'flex';
        document.getElementById('nav-profile').style.display = 'flex';
        document.getElementById('nav-logout').style.display = 'flex';

        // Show admin panel for admin users
        const adminNav = document.getElementById('nav-admin');
        if (adminNav) {
            adminNav.style.display = this.currentUser && this.currentUser.role === 'admin' ? 'flex' : 'none';
        }

        // Show user info
        document.getElementById('user-info').style.display = 'flex';



        this.updateUserInterface();

        // Load dashboard data
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboard();
        }
    }

    hideAllPages() {
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => page.classList.add('hidden'));
        
        // Remove active class from nav items
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => item.classList.remove('active'));
    }

    logout() {
        const userName = this.currentUser?.fullName || this.currentUser?.username || 'User';

        // Clear token and user data
        api.setToken(null);
        this.currentUser = null;

        // Clear any cached data
        localStorage.removeItem('user');

        // Show login page
        this.showLoginPage();

        showInfoNotification('Logged Out Successfully', `Goodbye, ${userName}. Your session has been ended securely.`);
    }

    getCurrentUser() {
        return this.currentUser;
    }

    isLoggedIn() {
        return !!this.currentUser;
    }

    hasRole(role) {
        if (!this.currentUser) return false;
        
        // Admin has access to everything
        if (this.currentUser.role === 'admin') return true;
        
        return this.currentUser.role === role;
    }

    canUploadEvidence() {
        return this.hasRole('police') || this.hasRole('admin');
    }

    canViewEvidence() {
        return this.hasRole('police') || this.hasRole('lawyer') || this.hasRole('admin');
    }

    canVerifyEvidence() {
        return this.hasRole('police') || this.hasRole('lawyer') || this.hasRole('admin');
    }

    canAccessAdminPanel() {
        return this.hasRole('admin');
    }
}

// Create global auth manager instance
const authManager = new AuthManager();
window.authManager = authManager;


