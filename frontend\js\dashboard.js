// Dashboard Management
class DashboardManager {
    constructor() {
        this.stats = {
            totalEvidence: 0,
            verifiedEvidence: 0,
            recentUploads: 0,
            blockchainStatus: 'Checking...'
        };
        this.recentActivity = [];
    }

    async loadDashboard() {
        try {
            showLoading();
            
            // Load all dashboard data in parallel
            await Promise.all([
                this.loadStats(),
                this.loadRecentActivity(),
                this.loadBlockchainStatus()
            ]);
            
            this.updateDashboardUI();
        } catch (error) {
            console.error('Dashboard load error:', error);
            showToast('Error loading dashboard data', 'error');
        } finally {
            hideLoading();
        }
    }

    async loadStats() {
        try {
            // Get evidence statistics
            const evidenceResponse = await api.getEvidences({ limit: 1000 });
            
            if (evidenceResponse.success) {
                const evidences = evidenceResponse.evidences;
                this.stats.totalEvidence = evidences.length;
                this.stats.verifiedEvidence = evidences.filter(e => e.isVerified).length;
                
                // Count recent uploads (last 7 days)
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                
                this.stats.recentUploads = evidences.filter(e => 
                    new Date(e.createdAt) > sevenDaysAgo
                ).length;
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    }

    async loadRecentActivity() {
        try {
            // Get recent evidence for activity feed
            const response = await api.getEvidences({ limit: 10, page: 1 });
            
            if (response.success) {
                this.recentActivity = response.evidences.map(evidence => ({
                    type: 'upload',
                    title: `Evidence uploaded: ${evidence.originalName}`,
                    description: `Case ID: ${evidence.caseId}`,
                    timestamp: evidence.createdAt,
                    user: evidence.uploader?.fullName || evidence.uploader?.username,
                    icon: 'fas fa-upload',
                    status: evidence.isVerified ? 'verified' : 'pending'
                }));
            }
        } catch (error) {
            console.error('Error loading recent activity:', error);
        }
    }

    async loadBlockchainStatus() {
        try {
            const response = await api.getBlockchainStatus();
            
            if (response.success) {
                this.stats.blockchainStatus = response.blockchain.connected ? 'Connected' : 'Disconnected';
            } else {
                this.stats.blockchainStatus = 'Error';
            }
        } catch (error) {
            console.error('Error loading blockchain status:', error);
            this.stats.blockchainStatus = 'Error';
        }
    }

    updateDashboardUI() {
        // Update statistics cards
        this.updateStatsCards();
        
        // Update recent activity
        this.updateRecentActivity();
    }

    updateStatsCards() {
        const totalEvidenceEl = document.getElementById('total-evidence');
        const verifiedEvidenceEl = document.getElementById('verified-evidence');
        const recentUploadsEl = document.getElementById('recent-uploads');
        const blockchainStatusEl = document.getElementById('blockchain-status');

        if (totalEvidenceEl) {
            totalEvidenceEl.textContent = this.stats.totalEvidence;
        }

        if (verifiedEvidenceEl) {
            verifiedEvidenceEl.textContent = this.stats.verifiedEvidence;
        }

        if (recentUploadsEl) {
            recentUploadsEl.textContent = this.stats.recentUploads;
        }

        if (blockchainStatusEl) {
            blockchainStatusEl.textContent = this.stats.blockchainStatus;
            
            // Add status color
            blockchainStatusEl.className = '';
            if (this.stats.blockchainStatus === 'Connected') {
                blockchainStatusEl.classList.add('text-success');
            } else if (this.stats.blockchainStatus === 'Disconnected') {
                blockchainStatusEl.classList.add('text-warning');
            } else {
                blockchainStatusEl.classList.add('text-danger');
            }
        }
    }

    updateRecentActivity() {
        const activityList = document.getElementById('activity-list');
        if (!activityList) return;

        if (this.recentActivity.length === 0) {
            activityList.innerHTML = `
                <div class="activity-item">
                    <div class="activity-title">No recent activity</div>
                    <div class="activity-meta">Start by uploading some evidence</div>
                </div>
            `;
            return;
        }

        const activityHTML = this.recentActivity.map(activity => `
            <div class="activity-item">
                <div class="activity-header">
                    <i class="${activity.icon}"></i>
                    <div class="activity-title">${activity.title}</div>
                    <span class="activity-status ${activity.status}">
                        ${activity.status === 'verified' ? 
                            '<i class="fas fa-check-circle"></i> Verified' : 
                            '<i class="fas fa-clock"></i> Pending'
                        }
                    </span>
                </div>
                <div class="activity-description">${activity.description}</div>
                <div class="activity-meta">
                    ${activity.user ? `By ${activity.user} • ` : ''}
                    ${formatDate(activity.timestamp)}
                </div>
            </div>
        `).join('');

        activityList.innerHTML = activityHTML;
    }

    // Refresh dashboard data
    async refresh() {
        await this.loadDashboard();
        showToast('Dashboard refreshed', 'success');
    }

    // Get dashboard statistics
    getStats() {
        return { ...this.stats };
    }

    // Get recent activity
    getRecentActivity() {
        return [...this.recentActivity];
    }
}

// Create global dashboard manager instance
const dashboardManager = new DashboardManager();

// Make it available globally for auth manager
window.dashboardManager = dashboardManager;
