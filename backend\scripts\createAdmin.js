const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import User model
const User = require('../models/User');

async function createAdminUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if admin already exists
    const existingAdmin = await User.findOne({ username: 'superadmin' });
    
    if (existingAdmin) {
      console.log('Super admin already exists!');
      console.log('Username: superadmin');
      console.log('You can reset the password if needed.');
      process.exit(0);
    }

    // Create super admin user
    const adminUser = new User({
      username: 'superadmin',
      email: '<EMAIL>',
      password: 'admin123456', // Will be hashed by the pre-save middleware
      fullName: 'System Administrator',
      firstName: 'System',
      lastName: 'Administrator',
      role: 'admin',
      department: 'IT Administration',
      isActive: true,
      walletAddress: null // Will be generated later if needed
    });

    await adminUser.save();

    console.log('✅ Super admin user created successfully!');
    console.log('');
    console.log('🔐 Admin Credentials:');
    console.log('Username: superadmin');
    console.log('Password: admin123456');
    console.log('Email: <EMAIL>');
    console.log('Role: admin');
    console.log('');
    console.log('🚨 IMPORTANT: Please change the password after first login!');
    console.log('');
    console.log('🎯 Admin Capabilities:');
    console.log('- Create, edit, and delete users');
    console.log('- Reset user passwords');
    console.log('- Activate/deactivate user accounts');
    console.log('- View system statistics');
    console.log('- Access all evidence and verification features');
    console.log('- Manage system settings');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script
createAdminUser();
