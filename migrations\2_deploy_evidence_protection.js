const EvidenceProtection = artifacts.require("EvidenceProtection");

module.exports = async function (deployer, network, accounts) {
  // Deploy the EvidenceProtection contract
  await deployer.deploy(EvidenceProtection);
  
  const evidenceProtection = await EvidenceProtection.deployed();
  
  console.log("EvidenceProtection deployed at:", evidenceProtection.address);
  
  // Grant roles to test accounts if in development
  if (network === 'development' || network === 'ganache') {
    console.log("Setting up test roles...");
    
    // Grant police role to account 1
    if (accounts[1]) {
      await evidenceProtection.grantPoliceRole(accounts[1]);
      console.log("Police role granted to:", accounts[1]);
    }
    
    // Grant lawyer role to account 2
    if (accounts[2]) {
      await evidenceProtection.grantLawyerRole(accounts[2]);
      console.log("Lawyer role granted to:", accounts[2]);
    }
    
    // Grant police role to account 3 (additional police officer)
    if (accounts[3]) {
      await evidenceProtection.grantPoliceRole(accounts[3]);
      console.log("Police role granted to:", accounts[3]);
    }
  }
  
  console.log("Migration completed successfully!");
};
